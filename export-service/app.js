import express from 'express';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
import exportRouter from './routes/export.js';

dotenv.config();
const app = express();
const port = process.env.PORT || 3000;

// Ensure export folder exists
fs.mkdirSync(process.env.EXPORT_DIR, { recursive: true });

app.use('/export', exportRouter);
app.use('/download', express.static(process.env.EXPORT_DIR));

app.listen(port, () => {
  console.log(`Export service running on http://localhost:${port}`);
});

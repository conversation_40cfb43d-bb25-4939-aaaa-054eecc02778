import fs from 'fs';
import { createObjectCsvWriter } from 'csv-writer';
import PDFDocument from 'pdfkit';
import ExcelJS from 'exceljs';

export async function writeCSV(path, data) {
  // Validate input data
  if (!Array.isArray(data)) {
    throw new Error('Data must be an array');
  }

  // Handle empty data case
  if (data.length === 0) {
    // Create empty CSV file with no headers
    const csvWriter = createObjectCsvWriter({
      path: path,
      header: []
    });
    return csvWriter.writeRecords([]);
  }

  // Extract headers from the first object
  const headers = Object.keys(data[0] || {});

  // Create CSV writer with correct parameters
  const csvWriter = createObjectCsvWriter({
    path: path,
    header: headers // csv-writer expects 'header', not 'headers'
  });

  // Write records and return the promise
  return csvWriter.writeRecords(data);
}

export async function writeExcel(path, data) {
  // Validate input data
  if (!Array.isArray(data)) {
    throw new Error('Data must be an array');
  }

  const workbook = new ExcelJS.Workbook();
  const sheet = workbook.addWorksheet('Export');

  // Handle empty data case
  if (data.length > 0) {
    const columns = Object.keys(data[0] || {}).map((key) => ({ header: key, key }));
    sheet.columns = columns;
    sheet.addRows(data);
  }

  await workbook.xlsx.writeFile(path);
}

export async function writePDF(path, data) {
  // Validate input data
  if (!Array.isArray(data)) {
    throw new Error('Data must be an array');
  }

  return new Promise((resolve, reject) => {
    const doc = new PDFDocument();
    const stream = fs.createWriteStream(path);
    doc.pipe(stream);

    if (data.length === 0) {
      doc.text('No data to export');
    } else {
      data.forEach((row, i) => {
        doc.text(`${i + 1}. ${JSON.stringify(row)}`);
        doc.moveDown(0.5);
      });
    }

    doc.end();
    stream.on('finish', resolve);
    stream.on('error', reject);
  });
}

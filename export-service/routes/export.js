import express from 'express';
import readline from 'readline';
import { writeCSV, writeExcel, writePDF } from '../utils/fileWriters.js';
import { v4 as uuidv4 } from 'uuid';
import fs from 'fs';

const router = express.Router();

router.post('/', async (req, res) => {
  const exportType = req.header('X-Export-Type') || 'csv';
  const fileId = uuidv4();
  const filePath = `${process.env.EXPORT_DIR}/${fileId}.${exportType}`;

  const rl = readline.createInterface({ input: req });
  const rows = [];

  rl.on('line', (line) => {
    try {
      const json = JSON.parse(line);
      console.log(json);
      rows.push(json);
    } catch (err) {
      console.error('Invalid JSON line:', line);
    }
  });

  rl.on('close', async () => {
    try {
      console.log(`Processing ${rows.length} rows for export type: ${exportType}`);

      if (exportType === 'csv') await writeCSV(filePath, rows);
      else if (exportType === 'xlsx') await writeExcel(filePath, rows);
      else if (exportType === 'pdf') await writePDF(filePath, rows);
      else return res.status(400).json({ error: 'Invalid export type' });

      res.json({
        file_id: fileId,
        download_url: `/download/${fileId}.${exportType}`
      });
    } catch (err) {
      console.error('Export error:', err);
      res.status(500).json({
        error: 'File export failed',
        details: err.message
      });
    }
  });
});

export default router;

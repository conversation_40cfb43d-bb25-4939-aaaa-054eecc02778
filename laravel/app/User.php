<?php

namespace App;

use App\Models\Location;
use App\Models\UnhashedPassword;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Staudenmeir\EloquentHasManyDeep\HasRelationships;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Traits\Users\UserLineStructureDivision;
use App\Models\Notificaion\NotificationCenter;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Traits\Users\ApprovalFlowFilterData;
use App\Traits\Users\ApprovableAndPlanable;
use App\Traits\Users\UserPositionDivision;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Storage;
use Spatie\Permission\Traits\HasRoles;
use Illuminate\Support\Facades\Auth;
use App\Traits\Reports\UsersFilter;
use App\Http\Menus\GetSidebarMenu;
use App\Traits\Users\IndexPerUser;
use Illuminate\Support\Collection;
use Laravel\Passport\HasApiTokens;
use App\Traits\Users\UserMessage;
use App\Models\DoctorProfiling;
use App\Traits\ModelExportable;
use App\Traits\ModelImportable;
use App\Traits\Users\UserData;
use Illuminate\Support\Carbon;
use App\Traits\PdfExportable;
use App\Services\UserStatus;
use Illuminate\Support\Str;
use App\Models\Material;
use App\Traits\SendMail;
use Carbon\CarbonPeriod;
use App\Models\Alert;
use App\Models\Trace;
use App\Models\UsageLog;

class User extends Authenticatable
{
    use Notifiable;
    use HasApiTokens;
    use SoftDeletes;
    use HasRoles;
    use ModelImportable;
    use HasRelationships;
    use ModelExportable;
    use PdfExportable;
    use SendMail;
    use UserLineStructureDivision;
    use ApprovableAndPlanable;
    use UserPositionDivision;
    use UserData;
    use IndexPerUser;
    use ApprovalFlowFilterData;
    use UserMessage;
    use UsersFilter;


    protected $guard_name = 'api';

    protected $table = 'users';

    protected $appends = ['url', 'is_manager'];
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'fullname',
        'is_vacant',
        'email',
        'email_verified_at',
        'personal_email',
        'personal_email_verified_at',
        'office_id',
        'password',
        'status',
        'menuroles',
        'remember_token',
        'api_token',
        'image',
        'file_id',
        'locked',
        'emp_code',
        'android_id',
        'hiring_date',
        'is_hidden',
        'device_token'
    ];


    // protected $appends = ['locked'];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password',
        'remember_token'
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'personal_email_verified_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected $attributes = [
        'menuroles' => 'user',
        // 'locked' => 'locked'
    ];

    public function unhashedPassword(): HasOne
    {
        return $this->hasOne(UnhashedPassword::class, 'user_id');
    }

    public function getUrlAttribute()
    {
        if ($this->image === null) return asset("img/avatars/male.png");
        return Storage::url($this->image);
    }

    public function getIsManagerAttribute()
    {
        return $this->divisions()->orderBy('division_type_id', 'ASC')->first()?->divisionType?->last_level == 0 ? 1 : 0;
    }

    public function scopeActive(Builder $query)
    {
        $query->where("status", 'active');
    }

    public function logActivities()
    {
        return $this->hasMany(LogActivity::class);
    }

    public function doctorProfiling()
    {
        return $this->hasMany(DoctorProfiling::class);
    }

    public function details()
    {
        return $this->hasMany(UserDetails::class);
    }

    public function lineusers()
    {
        return $this->hasMany(LineUser::class);
    }

    public function isAdmin()
    {
        return $this->hasPermissionTo(1);
    }

    public function materials()
    {
        $this->hasMany(Material::class);
    }

    public function lines($from = null, $to = null)
    {
        $from = $from ? Carbon::parse($from)->startOfDay() : Carbon::now()->startOfDay();
        $to = $to ? Carbon::parse($to)->endOfDay() : Carbon::now()->endOfDay();
        return $this->belongsToMany(Line::class, 'line_users', 'user_id', 'line_id')
            ->withPivot('from_date', 'to_date', 'deleted_at')
            ->where('line_users.deleted_at')
            ->where(function ($query) use ($from, $to) {
                $query->where(function ($subQuery) use ($from, $to) {
                    $subQuery->whereNull('line_users.to_date') // Active records
                        ->orWhereBetween('line_users.to_date', [$from->toDateString(), $to->toDateString()]) // Ends within range
                        ->orWhere('line_users.to_date', '>=', $to->toDateString()); // Ends within range
                })
                    ->where(function ($subQuery) use ($from, $to) {
                        $subQuery->where('line_users.from_date', '<=', $from->toDateString()) // Starts before range
                            ->orWhereBetween('line_users.from_date', [$from->toDateString(), $to->toDateString()]); // Starts within range
                    });
            });
        // ->where("line_users.from_date", "<=", $from ?? now())
        // ->where(fn($q) => $q->where('line_users.to_date', '>=', Carbon::parse($to)->toDateString() ?? (string)Carbon::now())
        //     ->orWhere('line_users.to_date', null));
    }

    public function allLines()
    {
        return $this->belongsToMany(Line::class, 'line_users', 'user_id', 'line_id')
            ->withPivot('from_date', 'to_date', 'deleted_at')
            ->where('line_users.deleted_at');
    }

    public function widgets()
    {
        return $this->belongsToMany(Widget::class, 'user_widget');
    }

    public function widgetModules()
    {
        return $this->hasManyDeepFromRelations($this->widgets(), (new Widget)->module())->distinct();
    }

    public static function getDivisionType($line)
    {
        return User::where('id', '=', Auth::id())->first()->lineusers->where('line_id', '=', $line->id)
            ->first()->lineDivisionUser->first()
            ->linedivision->DivisionType;
    }

    public function positions($from = null, $to = null): BelongsToMany
    {
        $from = $from ? Carbon::parse($from)->startOfDay() : Carbon::now()->startOfDay();
        $to = $to ? Carbon::parse($to)->endOfDay() : Carbon::now()->endOfDay();
        return $this->belongsToMany(Position::class, 'user_positions', 'user_id', 'position_id')->withPivot('from_date', 'to_date')
            ->where(function ($query) use ($from, $to) {
                $query->where(function ($subQuery) use ($from, $to) {
                    $subQuery->whereNull('user_positions.to_date') // Active records
                        ->orWhereBetween('user_positions.to_date', [$from->toDateString(), $to->toDateString()]) // Ends within range
                        ->orWhere('user_positions.to_date', '>=', $to->toDateString()); // Ends within range
                })
                    ->where(function ($subQuery) use ($from, $to) {
                        $subQuery->where('user_positions.from_date', '<=', $from->toDateString()) // Starts before range
                            ->orWhereBetween('user_positions.from_date', [$from->toDateString(), $to->toDateString()]); // Starts within range
                    });
            });
    }

    public function allPositions(): BelongsToMany
    {
        return $this->belongsToMany(Position::class, 'user_positions', 'user_id', 'position_id')
            ->withPivot('id', 'from_date', 'to_date');
    }

    public function userPosition($from = null, $to = null)
    {
        $from = $from ? Carbon::parse($from)->startOfDay() : Carbon::now()->startOfDay();
        $to = $to ? Carbon::parse($to)->endOfDay() : Carbon::now()->endOfDay();
        return $this->hasMany(UserPosition::class)
            ->where(function ($query) use ($from, $to) {
                $query->where(function ($subQuery) use ($from, $to) {
                    $subQuery->whereNull('user_positions.to_date') // Active records
                        ->orWhereBetween('user_positions.to_date', [$from->toDateString(), $to->toDateString()]) // Ends within range
                        ->orWhere('user_positions.to_date', '>=', $to->toDateString()); // Ends within range
                })
                    ->where(function ($subQuery) use ($from, $to) {
                        $subQuery->where('user_positions.from_date', '<=', $from->toDateString()) // Starts before range
                            ->orWhereBetween('user_positions.from_date', [$from->toDateString(), $to->toDateString()]); // Starts within range
                    });
            });
    }

    public function position($from = null, $to = null)
    {
        return $this->positions($from, $to)->wherePivot('user_id', '=', $this->id)->first();
    }

    public function hasPosition()
    {
        return $this->position() != null;
    }

    public function hasDivision(Line $line, $from = null, $to = null)
    {
        return $this->division($line, $from, $to);
    }

    public function hasDivisionOfAllLines()
    {
        return $this->divisions()->where('is_kol', 0)->exists();
    }

    public function traces(): HasMany
    {
        return $this->hasMany(Trace::class, 'user_id');
    }

    public function latestTrace(): HasOne
    {
        return $this->hasOne(Trace::class, 'user_id')->latest();
    }

    public static function getPermittedWidgets()
    {
        $permissions = User::where('id', '=', Auth::id())->first()->roles->pluck('permissions')->collapse();
        $permittedWidgets = [];
        foreach ($permissions as $permission) {
            $widget = Widget::where('name', '=', Str::title(str_replace('_', ' ', $permission->name)))->with('module')->first();
            if ($widget == null) continue;
            array_push($permittedWidgets, $widget);
        }
        return $permittedWidgets;
    }

    public static function getLineDivisionsOfAuthUser()
    {
        /**@var User $user */
        $user = Auth::user();
        $division_type = DivisionType::where('last_level', '=', 1)->value('id');
        $lines = $user->lines()->get();
        if ($user->hasRole('admin') || $user->hasRole('sub admin') || $user->hasRole('Gemstone Admin')) {
            $lines = Line::where('from_date', '<', (string)Carbon::now())
                ->where(
                    fn($q) => $q->where('to_date', '=', null)->OrWhere('to_date', '>', (string)Carbon::now())
                )->get();
        }
        $divisions = collect([]);
        foreach ($lines as $line) {
            $divisions = $divisions->push($user->userDivisions($line));
        }
        return $divisions->collapse()->where('division_type_id', $division_type)->where('is_kol', 0)->unique('id')->values()->pluck('id')->toArray();
    }

    public function getKol(Line $line)
    {
        $division_type = DivisionType::where('last_level', '=', 1)->value('id');
        return $this->divisions()->where('division_type_id', $division_type)->where('line_divisions.line_id', $line->id)->where('is_kol', 1)->first();
    }

    public function divisions($from = null, $to = null)
    {
        $from = $from ? Carbon::parse($from)->startOfDay() : Carbon::now()->startOfDay();
        $to = $to ? Carbon::parse($to)->endOfDay() : Carbon::now()->endOfDay();
        return $this->belongsToMany(LineDivision::class, 'line_users_divisions', 'user_id', 'line_division_id')
            ->withPivot('line_id', 'from_date', 'to_date', 'deleted_at')
            ->whereNull('line_users_divisions.deleted_at')
            ->where(function ($query) use ($from, $to) {
                $query->where(function ($subQuery) use ($from, $to) {
                    $subQuery->whereNull('line_users_divisions.to_date') // Active records
                        ->orWhereBetween('line_users_divisions.to_date', [$from->toDateString(), $to->toDateString()]) // Ends within range
                        ->orWhere('line_users_divisions.to_date', '>=', $to->toDateString()); // Ends within range
                })
                    ->where(function ($subQuery) use ($from, $to) {
                        $subQuery->where('line_users_divisions.from_date', '<=', $from->toDateString()) // Starts before range
                            ->orWhereBetween('line_users_divisions.from_date', [$from->toDateString(), $to->toDateString()]); // Starts within range
                    });
            });
        // ->where('line_users_divisions.from_date', '<=', $from ?? (string)Carbon::now())
        // ->where(fn($q) => $q->where('line_users_divisions.to_date', '>=', Carbon::parse($to)->toDateString() ?? (string)Carbon::now())
        //     ->orWhere('line_users_divisions.to_date', null));

    }
    public function allLineDivisions()
    {
        return $this->belongsToMany(LineDivision::class, 'line_users_divisions', 'user_id', 'line_division_id')
            ->withPivot('id', 'line_id', 'from_date', 'to_date', 'deleted_at')->whereNull('line_users_divisions.deleted_at');
    }

    public function activeAndNonActiveDivisions()
    {
        return $this->belongsToMany(LineDivision::class, 'line_users_divisions', 'user_id', 'line_division_id')
            ->withPivot('line_id', 'from_date', 'to_date', 'deleted_at')
            ->whereNull('line_users_divisions.deleted_at');
    }

    public function notificationCenters()
    {
        return $this->belongsToMany(NotificationCenter::class, 'user_notification_center', 'user_id', 'notification_center_id')->withTimestamps()->withPivot('value');
    }

    public function accounts()
    {
        return $this->hasManyDeepFromRelations($this->divisions(), (new LineDivision)->accounts());
    }

    public function doctors()
    {
        return $this->hasManyDeepFromRelations($this->accounts(), (new Account)->activeAccountDoctors());
    }

    public function accountTypes()
    {
        return $this->hasManyDeepFromRelations($this->accounts(), (new Account)->type());
    }

    public function division(?Line $line, $from = null, $to = null)
    {
        return $this->divisions($from, $to)->where('is_kol', 0)->wherePivot('line_id', '=', $line?->id)->first();
    }

    public function divisionType(Line $line, $from = null, $to = null)
    {
        // return $this->division($line)?->DivisionType;
        return $this->divisions($from, $to)->where('line_divisions.line_id', $line->id)
            ->where('is_kol', 0)->first()?->DivisionType;
    }

    public function allDivisionType(?Line $line)
    {
        return $this->activeAndNonActiveDivisions()->where('line_divisions.line_id', $line?->id)?->first()?->DivisionType;
    }

    public function planVisits()
    {
        return $this->hasMany(PlanVisit::class);
    }

    public function vacations()
    {
        return $this->hasMany(Vacation::class, 'user_id');
    }

    public static function vacationDates()
    {
        $dates = collect([]);
        $vacations = Vacation::where('user_id', Auth::id())
            ->where('full_day', 1)
            ->whereDate("from_date", '>=', Carbon::now()->startOfMonth()->toDateString())
            ->where("to_date", '<=', Carbon::now()->endOfMonth()->toDateString())
            ->whereHas('details', function ($q) {
                $q->where('approval', 1);
            })->get()->each(function ($vacation) use (&$dates) {
                $period = CarbonPeriod::create($vacation->from_date, $vacation->to_date);
                foreach ($period as $date) {
                    $dates->push($date->format('Y-m-d'));
                }
            });
        return $dates;
    }

    public function actualVisits()
    {
        return $this->hasMany(ActualVisit::class);
    }

    public function plans()
    {
        return $this->hasMany(PlanVisit::class);
    }

    public function officeWork()
    {
        return $this->hasMany(OwActualVisit::class);
    }

    public function status()
    {
        return new UserStatus($this);
    }

    public function positionDivision(Line $line = null)
    {
        $userPosition = $this->userPosition->first();
        $divisions = $userPosition->divisions->where('line_id', $line?->id);

        if (count($divisions) == 0) {
            $divisions = LineDivision::where('line_id', $line?->id)->where('division_type_id', 1)->whereNull('deleted_at')->get();
        }
        return $divisions;
    }

    public static function bricks(User $user, Line $line)
    {
        return $user->allBelowDivisions($line)->map(fn($division) => $division->bricks)->collapse();
    }

    public function startPlanDay()
    {
        return $this->belongsToMany(Line::class, 'user_start_plan_days', 'user_id', 'line_id')->withPivot('date');
    }

    public function restore()
    {
        $this->withTrashed()->where('id', $this->id)->restore();
        UserDetails::withTrashed()->where('user_id', $this->id)->restore();
        LineUser::withTrashed()->where('user_id', $this->id)->restore();
        LineDivisionUser::withTrashed()->where('user_id', $this->id)->restore();
    }

    public function forceDelete()
    {
        LineUser::withTrashed()->where('user_id', $this->id)->forceDelete();
        LineDivisionUser::withTrashed()->where('user_id', $this->id)->forceDelete();
        UserDetails::withTrashed()->where('user_id', $this->id)->forceDelete();
        $this->withTrashed()->where('id', $this->id)->forceDelete();
    }


    public function getMenus(): Collection
    {
        $menus = new GetSidebarMenu();
        $menuList = $menus->get($this->menuroles, 'en', 'sidebar menu');
        return collect($menuList)
            ->map(function ($item) {
                if ($item["slug"] == "dropdown") {

                    $links = collect($item["elements"])->filter(function ($element) {
                        // throw new CrmException(Str::slug("show " . $element["name"], "_"));
                        return $this->hasAnyPermission([
                            "all_permissions",
                            Str::slug("show_menu " . $element["name"], "_"),
                        ]);
                    })->values();

                    $links = $links->map(function ($subLink) {
                        if ($subLink['slug'] == "dropdown") {
                            $subLinks = collect($subLink["elements"])->filter(function ($element) {
                                return $this->hasAnyPermission([
                                    "all_permissions",
                                    Str::slug("show_menu " . $element["name"], "_"),
                                    Str::slug("show_menu_link " . $element["name"], "_"),
                                ]);
                            })->values();
                            $subLink["elements"] = $subLinks;
                        }
                        return $subLink;
                    })->values();

                    $item["elements"] = $links;
                }
                return $item;
            })
            ->filter(function ($value) {
                return $this->hasAnyPermission([
                    "all_permissions",
                    Str::slug("show_menu " . $value['name'], "_"),
                ]);
            });
    }

    public function hasVisits($date, $accountTypes)
    {
        return $this->actualVisits()->whereDate('visit_date', Carbon::parse($date)->toDateString())
            ->whereIn('acc_type_id', $accountTypes)->exists();
    }

    public function hasOw(string $date, int $shift = null): bool
    {
        return $this->officeWork()->whereDate('date', Carbon::parse($date)->toDateString())
            ->where(fn($q) => $q->where('shift_id', $shift)->orWhere('shift_id', null))->exists();
    }

    public function hasVacations($key, $shift, $month, $year)
    {
        $count = 0;
        $targetDate = Carbon::parse($key);

        // Keep parameters for backward compatibility but use more accurate logic
        $this->vacations()->where(fn($q) => $q->where('shift_id', $shift)->orWhere('shift_id', null))
            ->where(function ($query) use ($targetDate) {
                // Check if the target date falls within any vacation period
                $query->where('from_date', '<=', $targetDate->toDateString())
                      ->where('to_date', '>=', $targetDate->toDateString());
            })
            ->whereHas('details', fn($q) => $q->where('approval', 1))
            ->get()
            ->each(function ($vacation) use ($targetDate, &$count) {
                // Double-check that the target date is within the vacation period
                if ($targetDate->between($vacation->from_date, $vacation->to_date)) {
                    $count = 1;
                }
            });

        // Suppress unused parameter warnings - kept for backward compatibility
        unset($month, $year);

        return $count;
    }

    public function alert()
    {
        return $this->belongsToMany(Alert::class, 'alert_users', 'user_id', 'alert_id');
    }

    public function locations(): HasMany
    {
        return $this->hasMany(Trace::class);
    }
}

<?php

namespace App\Http\Controllers;

use App\Action;
use App\Form;
use App\Helpers\LogActivity;
use App\Http\Controllers\Controller;
use App\Http\Requests\CostTypeRequest;
use App\Models\CommercialRequest\Costs\Cost;
use App\Models\CommercialRequest\Costs\CostType;
use App\Permission;
use Illuminate\Http\Request;

class CommercialCostController extends ApiController
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $costs = Cost::get(['id','name']);
        $types = CostType::get()->map(function($item){
            return [
                'id'=>$item->id,
                'cost'=>$item->cost->name,
                'type_name'=>$item->name,
            ];
        });
        
        
        
        
        
        
        LogActivity::addLog();
        return response()->json(['costs'=>$costs,'types'=>$types]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(CostTypeRequest $request)
    {
        $cost_type = CostType::create([
            'name'=>$request->name,
            'cost_id'=>$request->cost,
        ]);
        
        
        
        
        
        
        $model_id = $cost_type->id;
        $model_type = CostType::class;

        LogActivity::addLog($model_id,$model_type);
        return $this->respondCreated();
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $cost = CostType::where('id',$id)->get()->map(function($type){
            return [
                'name'=> $type->name,
                'cost'=>$type->cost->id
            ];
        })->collapse();
        
        
        
        
        
        
        $model_id = $id;
        $model_type = CostType::class;
        LogActivity::addLog($model_id,$model_type);

        return $this->respond($cost);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(CostTypeRequest $request, $id)
    {
        $type = CostType::findOrFail($id);
        $type->name = $request->name;
        $type->cost_id = $request->cost;
        $type->save();
        
        
        
        
        
        
        $model_id = $type->id;
        $model_type = CostType::class;
        LogActivity::addLog($model_id,$model_type);
        return $this->respondSuccess();
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $model_id = $id;
        CostType::find($id)->delete();
        $model_type = CostType::class;
        LogActivity::addLog($model_id,$model_type);
        return $this->respondSuccess();
    }
}

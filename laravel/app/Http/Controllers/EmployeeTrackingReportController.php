<?php

namespace App\Http\Controllers;

use App\AccountType;
use App\ActualVisit;
use App\DivisionType;
use App\Exceptions\CrmException;
use App\Line;
use App\Models\OffDay;
use App\OwActualVisit;
use App\PublicHoliday;
use App\Shift;
use App\Vacation;
use Carbon\CarbonPeriod;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class EmployeeTrackingReportController extends ApiController
{
    public function filter(Request $request)
    {
        /**@var User authUser */
        $authUser = Auth::user();
        $tracking = $request->trackingFilter;
        $from = Carbon::parse($tracking['fromDate'])->startOfDay();
        $to = Carbon::parse($tracking['toDate'])->endOfDay();
        $year = Carbon::parse($tracking['fromDate'])->format('Y');
        $showOnlyNoShowData = $tracking['includeNoShowData'] ?? false;

        $dates = collect([]);
        $datesFields = collect([]);
        $period = CarbonPeriod::create($from, $to);

        // If filtering for no-show data only, we'll determine the dates after processing
        if (!$showOnlyNoShowData) {
            // Normal behavior: include all dates
            foreach ($period as $date) {
                $dates->put($date->format('m/d'), "");
                $datesFields->push($date->format('m/d'));
            }
        }
        $shifts = Shift::when(!empty($tracking['shifts']), fn($q) => $q->whereIntegerInRaw("shifts.id", $tracking['shifts']))->get();
        $lines = Line::when(!empty($tracking['lines']), fn($q) => $q->whereIntegerInRaw("lines.id", $tracking['lines']))->get();
        $lineIds = $lines->pluck('id');

        $filtered = new Collection([]);
        $data = new Collection([]);

        foreach ($lines as $line) {
            $users = $line->users($from, $to)
                ->when(!empty($tracking['users']), fn($q) => $q->whereIntegerInRaw("line_users.user_id", $tracking['users']))->get();
            $filtered = $filtered->merge($authUser->filterUsers($line, $users, $tracking));
        }

        // If filtering for no-show data only, collect no-show dates first
        if ($showOnlyNoShowData) {
            $noShowDates = collect([]);

            // Process each user to find dates with no-show data
            $filtered->unique('id')->values()->each(function ($user) use ($lineIds, $from, $to, $shifts, $year, $period, &$noShowDates) {
                foreach ($shifts as $shift) {
                    $accountTypes = AccountType::where('shift_id', $shift->id)->get()->pluck('id');

                    foreach ($period as $date) {
                        $dateKey = $date->format('m/d');
                        $dateString = Carbon::parse($date)->format($year . '-m-d');
                        $month = Carbon::parse($dateString)->format('m');

                        // Check if this date has no-show data for this user/shift
                        if (
                            !$user->hasVisits($dateString, $accountTypes) &&
                            !$user->hasVacations($dateString, $shift->id, $month, $year) &&
                            !$user->hasOw($dateString, $shift->id) &&
                            !OffDay::isOffDay($from, $to, $dateString, $shift->id, $lineIds[0]) &&
                            !PublicHoliday::isPublicHoliday($from, $to, $dateString, $lineIds[0])
                        ) {
                            $noShowDates->put($dateKey, "");
                        }
                    }
                }
            });

            // Set dates and datesFields to only include no-show dates
            $dates = $noShowDates;
            $datesFields = $noShowDates->keys();
        }

        $fields = collect(['line', 'employee','code', 'position', 'shift'])->merge($datesFields);

        // Process data with the determined dates
        $filtered->unique('id')->values()->each(function ($object) use ($lineIds, $data, $tracking, $from, $to, $dates, $shifts, $year) {
            $data = $data->push($this->statistics($lineIds, $object,  $from, $to, $tracking, $dates, $shifts, $year));
        });

        return response()->json([
            'data' => collect($data)->collapse(),
            'datesFields' => $datesFields,
            'fields' => $fields,
        ]);
    }

    private function statistics($lineIds, $user, $from, $to, $tracking, Collection $dates, $shifts, $year)
    {
        // Check if only no-show data should be shown
        $showOnlyNoShowData = $tracking['includeNoShowData'] ?? false;

        $shifts = $shifts->map(function ($shift) use ($lineIds, $user, $dates, $from, $to, $year, $showOnlyNoShowData) {
            $accountTypes = AccountType::where('shift_id', $shift->id)->get()->pluck('id');
            $dates = collect($dates)->map(function ($value, $key) use ($year, $user, $shift, $accountTypes, $from, $to, $lineIds, $showOnlyNoShowData) {
                $key = Carbon::parse($key)->format($year . '-m-d');
                $month = Carbon::parse($key)->format('m');

                // If filtering for no-show only, only return no-show data
                if ($showOnlyNoShowData) {
                    if (
                        !$user->hasVisits($key, $accountTypes) &&
                        !$user->hasVacations($key, $shift->id, $month, $year) &&
                        !$user->hasOw($key, $shift->id) &&
                        !OffDay::isOffDay($from, $to, $key, $shift->id, $lineIds[0]) &&
                        !PublicHoliday::isPublicHoliday($from, $to, $key, $lineIds[0])
                    ) {
                        return [
                            'data' => 'No',
                            'valueColor' => 'red'
                        ];
                    }
                    // Return empty for non-no-show dates when filtering
                    return "";
                }

                // Normal processing when not filtering for no-show only
                if ($user->hasVisits($key, $accountTypes)) {
                    $actual = ActualVisit::where('user_id', $user->id)
                        ->whereDate('visit_date', $key)
                        ->whereIn('acc_type_id', $accountTypes)->count() ?? 0;
                    if ($actual !== 0) {
                        return [
                            'data' => $actual,
                            'valueColor' => 'blue'
                        ];
                    }
                    return;
                }
                // throw new CrmException($key);
                if ($user->hasOw($key, $shift->id)) {
                    $ow = OwActualVisit::where('user_id', $user->id)
                        ->whereDate('date', $key)
                        ->where(fn($q) => $q->where('shift_id', $shift->id)->orWhere('shift_id', null))->count() ?? 0;
                    if ($ow !== 0) {
                        return [
                            'data' => 'ow',
                            'valueColor' => 'green'
                        ];
                    }
                    return;
                }
                if (OffDay::isOffDay($from, $to, $key, $shift->id, $lineIds[0])) {
                    return [
                        'data' => 'off',
                        'valueColor' => '#f39c12'
                    ];
                }
                if (PublicHoliday::isPublicHoliday($from, $to, $key, $lineIds[0])) {
                    return [
                        'data' => 'H',
                        'valueColor' => '#FF00FF'
                    ];
                }

                if ($user->hasVacations($key, $shift->id, $month, $year)) {
                    $targetDate = Carbon::parse($key);
                    $vacation = Vacation::where('user_id', $user->id)
                        ->where(fn($q) => $q->where('shift_id', $shift->id)->orWhere('shift_id', null))
                        ->where('from_date', '<=', $targetDate->toDateString())
                        ->where('to_date', '>=', $targetDate->toDateString())
                        ->whereHas('details', fn($q) => $q->where('approval', 1))->count() ?? 0;
                    // throw new CrmException($vacation);
                    if ($vacation !== 0) {
                        return [
                            'data' => 'v',
                            'valueColor' => 'brown'
                        ];
                    }
                    // return;
                }
                if (
                    !$user->hasVisits($key, $accountTypes) &&
                    !$user->hasVacations($key, $shift->id, $month, $year) &&
                    !$user->hasOw($key, $shift->id) &&
                    !OffDay::isOffDay($from, $to, $key, $shift->id, $lineIds[0]) &&
                    !PublicHoliday::isPublicHoliday($from, $to, $key, $lineIds[0])
                ) {
                    return [
                        'data' => 'No',
                        'valueColor' => 'red'
                    ];
                }
                return "";
            });
            return [
                'id' => $user->id,
                'line' => $user->lines()->whereIntegerInRaw('line_users.line_id', $lineIds)->pluck('name')->implode(','),
                'division' => $user->divisions()->whereIntegerInRaw('line_divisions.line_id', $lineIds)->pluck('name')->implode(','),
                'position' => $user->menuroles,
                'employee' => $user->fullname,
                'code' => $user->emp_code ?? '',
                'shift' => $shift->name,
                'shift_id' => $shift->id,
                'color' =>  $user->hasPosition() ? $user->positionDivision()->first()?->DivisionType->color : $user->divisions()?->first()?->DivisionType->color,
                ...$dates
            ];
        });
        return $shifts;
    }

    public function excel(Request $request)
    {
        /**@var User authUser */
        $authUser = Auth::user();
        $tracking = $request->trackingFilter;
        $from = Carbon::parse($tracking['fromDate'])->startOfDay();
        $to = Carbon::parse($tracking['toDate'])->endOfDay();
        $year = Carbon::parse($tracking['fromDate'])->format('Y');
        $showOnlyNoShowData = $tracking['includeNoShowData'] ?? false;

        $period = CarbonPeriod::create($from, $to);
        $periodMonths = CarbonPeriod::create($from, '1 month', $to);

        // Get the months and format them as you need
        $months = [];
        foreach ($periodMonths as $date) {
            $months[] = $date->format('F'); // 'F' gives full month name, use 'm' for month number
        }
        $dates[] = [
            'Month' => implode(', ', $months),
            'Year' => $year,
        ];

        $shifts = Shift::when(!empty($tracking['shifts']), fn($q) => $q->whereIntegerInRaw("shifts.id", $tracking['shifts']))->get();
        $lines = Line::when(!empty($tracking['lines']), fn($q) => $q->whereIntegerInRaw("lines.id", $tracking['lines']))->get();
        $lineIds = $lines->pluck('id');

        $filtered = new Collection([]);
        $data = new Collection([]);

        foreach ($lines as $line) {
            $users = $line->users($from, $to)
                ->when(!empty($tracking['users']), fn($q) => $q->whereIntegerInRaw("line_users.user_id", $tracking['users']))->get();
            $filtered = $filtered->merge($authUser->filterUsers($line, $users, $tracking));
        }

        // If filtering for no-show data only, filter the period to only include no-show dates
        if ($showOnlyNoShowData) {
            $noShowDates = collect([]);

            // Process each user to find dates with no-show data
            $filtered->unique('id')->values()->each(function ($user) use ($lineIds, $from, $to, $shifts, $year, $period, &$noShowDates) {
                foreach ($shifts as $shift) {
                    $accountTypes = AccountType::where('shift_id', $shift->id)->get()->pluck('id');

                    foreach ($period as $date) {
                        $dateString = Carbon::parse($date)->format($year . '-m-d');
                        $month = Carbon::parse($dateString)->format('m');

                        // Check if this date has no-show data for this user/shift
                        if (
                            !$user->hasVisits($dateString, $accountTypes) &&
                            !$user->hasVacations($dateString, $shift->id, $month, $year) &&
                            !$user->hasOw($dateString, $shift->id) &&
                            !OffDay::isOffDay($from, $to, $dateString, $shift->id, $lineIds[0]) &&
                            !PublicHoliday::isPublicHoliday($from, $to, $dateString, $lineIds[0])
                        ) {
                            $noShowDates->push($date);
                        }
                    }
                }
            });

            // Update period to only include no-show dates
            $period = $noShowDates->unique();
        }

        $filtered->unique('id')->values()->each(function ($object) use ($period, $lineIds, $data, $from, $to, $shifts, $year, $tracking) {
            $data = $data->push($this->statisticEexcel($period, $lineIds, $object, $from, $to, $shifts, $year, $tracking));
        });

        return response()->json([
            'data' => $data->collapse(),
            'dates' => $dates,
        ]);
    }

    private function statisticEexcel($period, $lineIds, $user, $from, $to, $shifts, $year, $tracking)
    {
        // Check if only no-show data should be shown
        $showOnlyNoShowData = $tracking['includeNoShowData'] ?? false;

        $lines = $user->lines()->whereIntegerInRaw('line_users.line_id', $lineIds)->pluck('name');
        $divisions = $user->divisions()->whereIntegerInRaw('line_divisions.line_id', $lineIds)->pluck('name');
        $color = $user->hasPosition() ? $user->positionDivision()->first()?->DivisionType->color : $user->divisions()?->first()?->DivisionType->color;
        $data = collect([]);

        foreach ($shifts as $shift) {
            $row = collect([
                'id' => $user->id,
                'line' => $lines->implode(','),
                'division' => $divisions->implode(','),
                'position' => $user->menuroles,
                'employee' => $user->fullname,
                'code' => $user->emp_code,
                'shift' => $shift->name,
                'shift_id' => $shift->id,
                'color' =>  $color
            ]);
            $accountTypes = AccountType::where('shift_id', $shift->id)->get()->pluck('id');

            foreach ($period as $date) {
                $formated = Carbon::parse($date)->format('m/d');
                $month = Carbon::parse($date)->format('m');

                // If filtering for no-show only, only process no-show cases
                if ($showOnlyNoShowData) {
                    if (
                        !$user->hasVisits($date, $accountTypes) &&
                        !$user->hasVacations($date, $shift->id, $month, $year) &&
                        !$user->hasOw($date, $shift->id) &&
                        !OffDay::isOffDay($from, $to, $date, $shift->id, $lineIds[0]) &&
                        !PublicHoliday::isPublicHoliday($from, $to, $date, $lineIds[0])
                    ) {
                        $row = $row->put($formated, 'No Show');
                    }
                    continue;
                }

                // Normal processing when not filtering for no-show only
                if ($user->hasVisits($date, $accountTypes)) {
                    $actual = ActualVisit::where('user_id', $user->id)
                        ->whereDate('visit_date', Carbon::parse($date)->toDateString())
                        ->whereIn('acc_type_id', $accountTypes)->count() ?? 0;
                    if ($actual !== 0) {
                        $row = $row->put($formated, '(visits) ' . $actual);
                    }
                    continue;
                }
                if ($user->hasOw($date, $shift->id)) {
                    $ow = OwActualVisit::where('user_id', $user->id)
                        ->whereDate('date', Carbon::parse($date)->toDateString())
                        ->where(fn($q) => $q->where('shift_id', $shift->id)->orWhere('shift_id', null))->count() ?? 0;
                    if ($ow !== 0) {
                        $row = $row->put($formated, '(ow) ' . $ow);
                    }
                    continue;
                }
                if (OffDay::isOffDay($from, $to, $date, $shift->id, $lineIds[0])) {
                    $row = $row->put($formated, 'Off Day');
                    continue;
                }
                if (PublicHoliday::isPublicHoliday($from, $to, $date, $lineIds[0])) {
                    $row = $row->put($formated, 'Public Holiday');
                    continue;
                }

                if ($user->hasVacations($date, $shift->id, $month, $year)) {
                    $targetDate = Carbon::parse($date);
                    $vacation = Vacation::where('user_id', $user->id)
                        ->where(fn($q) => $q->where('shift_id', $shift->id)->orWhere('shift_id', null))
                        ->where('from_date', '<=', $targetDate->toDateString())
                        ->where('to_date', '>=', $targetDate->toDateString())
                        ->whereHas('details', fn($q) => $q->where('approval', 1))->count() ?? 0;
                    if ($vacation !== 0) {
                        $row = $row->put($formated, 'Vacation');
                    }
                    continue;
                }
                if (
                    !$user->hasVisits($date, $accountTypes) &&
                    !$user->hasVacations($date, $shift->id, $month, $year) &&
                    !$user->hasOw($date, $shift->id) &&
                    !OffDay::isOffDay($from, $to, $date, $shift->id, $lineIds[0]) &&
                    !PublicHoliday::isPublicHoliday($from, $to, $date, $lineIds[0])
                ) {
                    $row = $row->put($formated, 'No Show');
                    continue;
                }
            }
            $data = $data->push($row);
        }
        return $data;
    }
}

import ExcelJS from 'exceljs';
import { saveAs } from 'file-saver';

export default {
    async DownloadStyledExcelChuncked(dates, items, headerItems, title, subTitle) {
        // Check dataset size and use appropriate method
        const itemCount = items ? items.length : 0;
        const columnCount = headerItems ? headerItems.length : 0;

        // Memory estimation: roughly 100 bytes per cell
        const estimatedMemoryMB = (itemCount * columnCount * 100) / (1024 * 1024);

        console.log(`Excel Download: ${itemCount} rows, ${columnCount} columns, ~${estimatedMemoryMB.toFixed(1)}MB estimated`);

        // Determine if sequential file processing is needed
        const fileConfig = this.calculateSequentialFileConfiguration(itemCount, columnCount, estimatedMemoryMB);

        if (fileConfig.useSequentialFiles) {
            console.log(`Sequential file processing: ${fileConfig.totalFiles} files, ${fileConfig.rowsPerFile} rows per file`);
            this.flash(`Large dataset will be split into ${fileConfig.totalFiles} separate Excel files for optimal performance`, 'info');
        }

        // Use Web Worker for very large datasets
        if (itemCount > 100000 || estimatedMemoryMB > 200) {
            return this.downloadStyledExcelWithWorker(dates, items, headerItems, title, subTitle, fileConfig);
        }

        // Use chunked processing for large datasets
        if (itemCount > 50000 || estimatedMemoryMB > 100) {
            return this.downloadStyledExcelChunked(dates, items, headerItems, title, subTitle, fileConfig);
        }

        // Use original method for smaller datasets
        return this.downloadStyledExcelOriginal(dates, items, headerItems, title, subTitle);
    },

    calculateSequentialFileConfiguration(itemCount, columnCount, estimatedMemoryMB) {
        // Base configuration
        const config = {
            useSequentialFiles: false,
            totalFiles: 1,
            rowsPerFile: itemCount,
            maxRowsPerFile: 50000, // Safe default for single file processing
            files: []
        };

        // Determine if sequential files are needed based on various factors
        const memoryThreshold = 120; // MB (lower than multi-sheet since we're processing sequentially)
        const rowThreshold = 60000; // rows (lower threshold for better performance)
        const cellThreshold = 1500000; // total cells (rows * columns)

        const totalCells = itemCount * columnCount;
        const needsSequentialFiles = (
            itemCount > rowThreshold ||
            estimatedMemoryMB > memoryThreshold ||
            totalCells > cellThreshold
        );

        if (!needsSequentialFiles) {
            return config;
        }

        // Calculate optimal rows per file (more conservative for sequential processing)
        let optimalRowsPerFile = config.maxRowsPerFile;

        // Adjust based on column count (more aggressive reduction for sequential files)
        if (columnCount > 50) {
            optimalRowsPerFile = Math.min(20000, optimalRowsPerFile);
        } else if (columnCount > 30) {
            optimalRowsPerFile = Math.min(30000, optimalRowsPerFile);
        } else if (columnCount > 20) {
            optimalRowsPerFile = Math.min(35000, optimalRowsPerFile);
        } else if (columnCount > 15) {
            optimalRowsPerFile = Math.min(40000, optimalRowsPerFile);
        }

        // Adjust based on memory estimation (more conservative)
        if (estimatedMemoryMB > 250) {
            optimalRowsPerFile = Math.min(15000, optimalRowsPerFile);
        } else if (estimatedMemoryMB > 180) {
            optimalRowsPerFile = Math.min(25000, optimalRowsPerFile);
        } else if (estimatedMemoryMB > 120) {
            optimalRowsPerFile = Math.min(35000, optimalRowsPerFile);
        }

        // Calculate number of files needed
        const totalFiles = Math.ceil(itemCount / optimalRowsPerFile);

        // Build file configuration
        config.useSequentialFiles = true;
        config.totalFiles = totalFiles;
        config.rowsPerFile = optimalRowsPerFile;

        for (let i = 0; i < totalFiles; i++) {
            const startRow = i * optimalRowsPerFile;
            const endRow = Math.min(startRow + optimalRowsPerFile, itemCount);
            const actualRows = endRow - startRow;

            config.files.push({
                index: i,
                fileName: `UnifiedSalesDetails_Part${i + 1}.xlsx`,
                displayName: `Part ${i + 1}`,
                startRow,
                endRow,
                rowCount: actualRows
            });
        }

        console.log('Sequential file configuration:', config);
        return config;
    },

    async downloadStyledExcelOriginal(dates, items, headerItems, title, subTitle) {
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet(title); // Create worksheet

        const colorMapping = {
            green: '008000', // Excel-compatible ARGB hex for green
            red: 'FF0000', // Excel-compatible ARGB hex for red
            blue: '0000FF', // Excel-compatible ARGB hex for blue
            black: '000000', // Excel-compatible ARGB hex for blue
            // Add more color mappings as needed
        };
        // Styles
        const headerStyle = {
            font: { bold: true, color: { argb: 'FFFFFF' }, size: 11 },
            fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: '002060' } },
            alignment: { horizontal: 'center', vertical: 'middle' },
        };

        const titleStyle = {
            font: { bold: true, color: { argb: '002060' }, size: 16 },
            alignment: { horizontal: 'center', vertical: 'middle' },
        };
        // Title in row 2, merged across D2:F2
        worksheet.mergeCells('D2:F2');
        const titleCell = worksheet.getCell('D2');
        titleCell.value = title;
        titleCell.style = titleStyle;

        const addDataWithHeaders = (data, startRow, startCol, mergedRows = 5) => {
            const headers = Object.keys(data[0]);
            headers.forEach((header, index) => {
                if (index === 0) {
                    // Merge first header across two columns
                    worksheet.mergeCells(startRow, startCol, startRow, startCol + 1);
                    const headerCell = worksheet.getCell(startRow, startCol);
                    headerCell.value = header;
                    headerCell.style = headerStyle;

                    // Merge first value cell across two columns
                    worksheet.mergeCells(startRow + 1, startCol, startRow + mergedRows, startCol + 1);
                    const valueCell = worksheet.getCell(startRow + 1, startCol);
                    valueCell.value = data[0][header];
                    valueCell.alignment = { horizontal: 'center', vertical: 'middle' };
                    valueCell.font = { bold: true };
                } else {
                    // For other headers and values, add normally
                    const headerCell = worksheet.getCell(startRow, startCol + index + 1);
                    headerCell.value = header;
                    headerCell.style = headerStyle;
                    worksheet.getColumn(startCol + index + 1).width = 10; // Set column width

                    const valueCell = worksheet.getCell(startRow + 1, startCol + index + 1);
                    valueCell.value = data[0][header];
                    valueCell.alignment = { horizontal: 'center', vertical: 'middle' };
                    valueCell.font = { bold: true };
                    worksheet.mergeCells(startRow + 1, startCol + index + 1, startRow + mergedRows, startCol + index + 1);
                }
            });
        };

        // Data1 at row 5
        addDataWithHeaders(dates, 5, 1);

        worksheet.mergeCells('A12:D12');
        const operationalTitleCell = worksheet.getCell('A12');
        operationalTitleCell.value = subTitle;
        operationalTitleCell.style = headerStyle;

        // Data3 Headers at row 14 and 15 (merged)
        // const headers3 = Object.keys(data3[0]);
        headerItems.forEach((header, index) => {
            worksheet.mergeCells(14, 1 + index, 15, 1 + index); // Merge cells for headers
            const headerCell = worksheet.getCell(14, 1 + index);
            headerCell.value = header.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()); // Convert _ to space and capitalize
            headerCell.style = headerStyle;
            worksheet.getColumn(1 + index).width = 10; // Set column width
        });

        // Data3 Values starting from row 16
        items.forEach((rowData, rowIndex) => {
            const row = worksheet.getRow(16 + rowIndex);
            row.height = 25;
            headerItems.forEach((header, colIndex) => {
                const cell = row.getCell(1 + colIndex);
                cell.value = rowData[header];
                cell.alignment = { horizontal: 'center', vertical: 'middle' };
                cell.font = { bold: true };
                if (rowData.color) {
                    this.employeeColoring(header, rowData, cell, colorMapping);
                }
                if (title == 'Overall Visits Report') {
                    this.overallVisits(header, rowData, cell);
                }
                if (title == 'Employee Tracking Report') {
                    this.employeeTracking(cell);
                }
                if (title == 'Structure Report') {
                    this.structureData(header, cell);
                }

            });
        });
        worksheet.columns.forEach(column => {
            column.width = 20; // Set a width for all columns
        });

        // Write to a file
        workbook.xlsx.writeBuffer().then(buffer => {
            saveAs(new Blob([buffer]), title + '.xlsx'); // Download file
        });
    },

    async downloadStyledExcelChunked(dates, items, headerItems, title, subTitle, fileConfig = null) {
        try {
            // Show loading state
            this.showExcelProcessingState(true, 'Preparing large Excel download...');

            // Validate inputs
            if (!items || !Array.isArray(items) || items.length === 0) {
                throw new Error('No data available for Excel generation');
            }

            if (!headerItems || !Array.isArray(headerItems) || headerItems.length === 0) {
                throw new Error('No column headers available for Excel generation');
            }

            // Use provided file configuration or calculate default
            const finalFileConfig = fileConfig || this.calculateSequentialFileConfiguration(
                items.length,
                headerItems.length,
                (items.length * headerItems.length * 100) / (1024 * 1024)
            );

            // Memory monitoring
            const startMemory = this.getMemoryUsage();
            let totalProcessedRows = 0;
            const totalRows = items.length;
            const chunkSize = this.calculateOptimalChunkSize(finalFileConfig.rowsPerFile, headerItems.length);

            console.log(`Sequential file processing: ${chunkSize} chunk size for ${totalRows} rows`);
            console.log(`File config:`, finalFileConfig);

            // Check if dataset is extremely large
            if (totalRows > 500000) {
                const proceed = confirm(`This dataset contains ${totalRows.toLocaleString()} records, which may take a very long time to process and could impact browser performance significantly. Consider using server-side export instead. Continue anyway?`);
                if (!proceed) {
                    this.showExcelProcessingState(false);
                    return;
                }
            }

            // Process files sequentially
            if (finalFileConfig.useSequentialFiles) {
                return await this.processSequentialFiles(dates, items, headerItems, title, subTitle, finalFileConfig, chunkSize, startMemory);
            }

            // Single file processing (original logic)
            const workbook = new ExcelJS.Workbook();
            const worksheet = workbook.addWorksheet(title);

            // Setup styles and headers
            await this.setupWorksheetStyles(worksheet, dates, headerItems, title, subTitle);

            // Process data in chunks
            let processedRows = 0;
            for (let i = 0; i < totalRows; i += chunkSize) {
                const chunk = items.slice(i, i + chunkSize);
                const progress = ((i + chunk.length) / totalRows) * 90;

                this.updateExcelProgress(progress, `Processing rows ${i + 1}-${Math.min(i + chunkSize, totalRows)} of ${totalRows}`);

                // Process chunk with memory monitoring
                await this.processDataChunk(worksheet, chunk, headerItems, title, i + 16); // +16 for header rows

                processedRows += chunk.length;

                // Memory check and cleanup
                const currentMemory = this.getMemoryUsage();
                if (currentMemory > startMemory * 2) {
                    console.warn('High memory usage detected, forcing garbage collection');
                    if (window.gc) window.gc(); // Force GC if available
                    await this.delay(100); // Allow GC to run
                }

                // Yield control to prevent UI blocking
                await this.delay(1);
            }

            // Finalize worksheet
            this.finalizeWorksheet(worksheet);

            // Generate and download file
            this.updateExcelProgress(95, 'Generating Excel file...');
            const buffer = await workbook.xlsx.writeBuffer();

            this.updateExcelProgress(100, 'Download starting...');
            saveAs(new Blob([buffer]), title + '.xlsx');

            this.showExcelProcessingState(false);
            this.showSuccessMessage(`Excel file with ${processedRows.toLocaleString()} rows generated successfully!`);

        } catch (error) {
            this.showExcelProcessingState(false);
            this.handleExcelError(error, items.length);
        }
    },

    async processSequentialFiles(dates, items, headerItems, title, subTitle, fileConfig, chunkSize, startMemory) {
        const downloadedFiles = [];
        const failedFiles = [];
        let totalProcessedRows = 0;

        try {
            // Initialize file tracking
            const fileTrackingInfo = {
                currentFile: 0,
                totalFiles: fileConfig.totalFiles
            };

            this.showExcelProcessingState(true, `Preparing ${fileConfig.totalFiles} sequential file downloads...`, fileTrackingInfo);

            // Process each file sequentially
            for (let fileIndex = 0; fileIndex < fileConfig.totalFiles; fileIndex++) {
                const fileInfo = fileConfig.files[fileIndex];
                const fileData = items.slice(fileInfo.startRow, fileInfo.endRow);

                // Update file tracking
                fileTrackingInfo.currentFile = fileIndex + 1;

                this.updateExcelProgress(
                    (fileIndex / fileConfig.totalFiles) * 90,
                    `Generating file ${fileIndex + 1} of ${fileConfig.totalFiles}: ${fileInfo.displayName}...`,
                    fileTrackingInfo
                );

                try {
                    // Create workbook for this file
                    const workbook = new ExcelJS.Workbook();
                    const worksheet = workbook.addWorksheet(title);

                    // Setup styles and headers for this file
                    await this.setupWorksheetStyles(worksheet, dates, headerItems, title, `${subTitle} - ${fileInfo.displayName}`);

                    // Process data in chunks for this file
                    let fileProcessedRows = 0;
                    for (let i = 0; i < fileData.length; i += chunkSize) {
                        const chunk = fileData.slice(i, i + chunkSize);
                        const fileProgress = (fileIndex / fileConfig.totalFiles) * 90 +
                                           ((i + chunk.length) / fileData.length) * (90 / fileConfig.totalFiles);

                        this.updateExcelProgress(
                            fileProgress,
                            `Processing ${fileInfo.displayName}: ${i + 1}-${Math.min(i + chunkSize, fileData.length)} of ${fileData.length} rows`,
                            fileTrackingInfo
                        );

                        // Process chunk with memory monitoring
                        await this.processDataChunk(worksheet, chunk, headerItems, title, i + 16); // +16 for header rows

                        fileProcessedRows += chunk.length;
                        totalProcessedRows += chunk.length;

                        // Memory check and cleanup
                        const currentMemory = this.getMemoryUsage();
                        if (currentMemory > startMemory * 1.5) { // More aggressive cleanup for sequential files
                            console.warn('High memory usage detected, forcing garbage collection');
                            if (window.gc) window.gc(); // Force GC if available
                            await this.delay(50); // Allow GC to run
                        }

                        // Yield control to prevent UI blocking
                        await this.delay(1);
                    }

                    // Finalize this worksheet
                    this.finalizeWorksheet(worksheet);

                    // Generate and download this file
                    this.updateExcelProgress(
                        (fileIndex / fileConfig.totalFiles) * 90 + (90 / fileConfig.totalFiles) * 0.9,
                        `Downloading ${fileInfo.displayName}...`,
                        fileTrackingInfo
                    );

                    const buffer = await workbook.xlsx.writeBuffer();
                    saveAs(new Blob([buffer]), fileInfo.fileName);

                    downloadedFiles.push(fileInfo.fileName);
                    console.log(`Successfully generated and downloaded: ${fileInfo.fileName} (${fileProcessedRows} rows)`);

                    // Clear workbook from memory
                    workbook.removeWorksheet(worksheet.id);

                    // Force garbage collection between files
                    if (window.gc) window.gc();
                    await this.delay(100); // Brief pause between files

                } catch (fileError) {
                    console.error(`Error processing file ${fileInfo.fileName}:`, fileError);
                    failedFiles.push({
                        fileName: fileInfo.fileName,
                        error: fileError.message
                    });

                    // Continue with next file
                    continue;
                }
            }

            // Final progress update
            this.updateExcelProgress(100, 'All files processed!');
            this.showExcelProcessingState(false);

            // Generate success/failure message
            let message = `Sequential download completed! ${downloadedFiles.length} of ${fileConfig.totalFiles} files downloaded successfully.`;
            if (failedFiles.length > 0) {
                message += ` ${failedFiles.length} files failed: ${failedFiles.map(f => f.fileName).join(', ')}`;
                this.flash(message, 'warning');
            } else {
                message += ` Total records processed: ${totalProcessedRows.toLocaleString()}.`;
                this.showSuccessMessage(message);
            }

        } catch (error) {
            this.showExcelProcessingState(false);
            this.handleExcelError(error, items.length);
        }
    },



    async downloadStyledExcelWithWorker(dates, items, headerItems, title, subTitle, fileConfig = null) {
        try {
            this.showExcelProcessingState(true, 'Initializing Web Worker for large dataset...');

            // Check if Web Workers are supported
            if (typeof Worker === 'undefined') {
                console.warn('Web Workers not supported, falling back to chunked processing');
                return this.downloadStyledExcelChunked(dates, items, headerItems, title, subTitle);
            }

            // Try to create Web Worker with proper error handling
            let worker;
            try {
                // Try different possible paths for the worker
                const workerPaths = [
                    '/workers/ExcelProcessingWorker.js',
                    './workers/ExcelProcessingWorker.js',
                    '../workers/ExcelProcessingWorker.js',
                    '/public/workers/ExcelProcessingWorker.js'
                ];

                let workerLoaded = false;
                for (const path of workerPaths) {
                    try {
                        worker = new Worker(path);
                        console.log(`Web Worker loaded successfully from: ${path}`);
                        workerLoaded = true;
                        break;
                    } catch (pathError) {
                        console.warn(`Failed to load worker from ${path}:`, pathError.message);
                    }
                }

                if (!workerLoaded) {
                    throw new Error('Could not load Web Worker from any path');
                }

            } catch (workerError) {
                console.warn('Web Worker creation failed:', workerError);
                return this.downloadStyledExcelChunked(dates, items, headerItems, title, subTitle);
            }

            // Use provided file configuration or calculate default
            const finalFileConfig = fileConfig || this.calculateSequentialFileConfiguration(
                items.length,
                headerItems.length,
                (items.length * headerItems.length * 100) / (1024 * 1024)
            );

            const totalRows = items.length;
            const chunkSize = this.calculateOptimalChunkSize(finalFileConfig.rowsPerFile, headerItems.length);
            let processedRows = 0;

            console.log('Web Worker sequential file config:', finalFileConfig);

            // Check if sequential files are needed
            if (finalFileConfig.useSequentialFiles) {
                // Use chunked processing for sequential files (Web Worker not optimal for multiple file downloads)
                console.log('Sequential files detected, falling back to chunked processing for better file management');
                return this.downloadStyledExcelChunked(dates, items, headerItems, title, subTitle, finalFileConfig);
            }

            // Set up worker timeout for single file processing
            const timeoutDuration = 30000; // 30s for single file
            const workerTimeout = setTimeout(() => {
                console.warn('Web Worker timeout, falling back to chunked processing');
                worker.terminate();
                this.downloadStyledExcelChunked(dates, items, headerItems, title, subTitle, finalFileConfig);
            }, timeoutDuration);

            return new Promise((resolve, reject) => {
                worker.onmessage = async (e) => {
                    const { type, data, error, buffer } = e.data;

                    try {
                        switch (type) {
                            case 'WORKBOOK_INITIALIZED':
                                clearTimeout(workerTimeout);
                                this.updateExcelProgress(5, 'Workbook initialized, processing data...');
                                await this.processDataWithWorker(worker, items, headerItems, title, chunkSize);
                                break;

                            case 'CHUNK_PROCESSED':
                                processedRows = data.processedRows;
                                const progress = 5 + (processedRows / totalRows) * 80; // 5-85% for data processing
                                this.updateExcelProgress(progress, `Processed ${processedRows} of ${totalRows} rows`);
                                break;

                            case 'WORKBOOK_FINALIZED':
                                this.updateExcelProgress(90, 'Generating Excel file...');
                                worker.postMessage({ type: 'GENERATE_BUFFER' });
                                break;

                            case 'BUFFER_GENERATED':
                                clearTimeout(workerTimeout);
                                this.updateExcelProgress(100, 'Download starting...');
                                saveAs(new Blob([buffer]), title + '.xlsx');
                                this.showExcelProcessingState(false);

                                // Generate success message
                                const successMessage = `Excel file with ${processedRows.toLocaleString()} rows generated successfully using Web Worker!`;
                                this.showSuccessMessage(successMessage);

                                worker.terminate();
                                resolve();
                                break;

                            case 'ERROR':
                                clearTimeout(workerTimeout);
                                // Check if it's an ExcelJS loading error
                                if (error.code === 'EXCELJS_LOAD_FAILED') {
                                    console.warn('ExcelJS failed to load in Web Worker, falling back to chunked processing');
                                    worker.terminate();
                                    this.showExcelProcessingState(false);
                                    resolve(this.downloadStyledExcelChunked(dates, items, headerItems, title, subTitle, finalFileConfig));
                                    return;
                                }
                                throw new Error(error.message);
                        }
                    } catch (err) {
                        clearTimeout(workerTimeout);
                        worker.terminate();
                        reject(err);
                    }
                };

                worker.onerror = (error) => {
                    clearTimeout(workerTimeout);
                    console.warn('Web Worker error:', error);
                    worker.terminate();
                    // Fall back to chunked processing instead of rejecting
                    this.showExcelProcessingState(false);
                    resolve(this.downloadStyledExcelChunked(dates, items, headerItems, title, subTitle, finalFileConfig));
                };

                // Initialize workbook
                worker.postMessage({
                    type: 'INIT_WORKBOOK',
                    data: { title, dates, headerItems, subTitle }
                });
            });

        } catch (error) {
            console.warn('Web Worker failed, falling back to chunked processing:', error);
            this.showExcelProcessingState(false);

            // Log the fallback for debugging
            this.logExcelFallback('Web Worker', 'Chunked Processing', error);

            // Show user-friendly message about fallback
            this.flash('Using alternative processing method for large dataset...', 'info');

            // Ensure we return the chunked processing result with file config
            const fallbackFileConfig = fileConfig || this.calculateSequentialFileConfiguration(
                items.length,
                headerItems.length,
                (items.length * headerItems.length * 100) / (1024 * 1024)
            );
            return await this.downloadStyledExcelChunked(dates, items, headerItems, title, subTitle, fallbackFileConfig);
        }
    },



    logExcelFallback(fromMethod, toMethod, error) {
        console.log(`Excel Processing Fallback: ${fromMethod} → ${toMethod}`);
        console.log('Fallback reason:', error.message);
        console.log('Fallback timestamp:', new Date().toISOString());
    },

    async processDataWithWorker(worker, items, headerItems, title, chunkSize) {
        const totalRows = items.length;

        for (let i = 0; i < totalRows; i += chunkSize) {
            const chunk = items.slice(i, i + chunkSize);
            const startRowIndex = 16 + i; // +16 for header rows

            worker.postMessage({
                type: 'PROCESS_CHUNK',
                data: { chunk, headerItems, title, startRowIndex }
            });

            // Wait for chunk to be processed
            await new Promise((resolve) => {
                const handler = (e) => {
                    if (e.data.type === 'CHUNK_PROCESSED') {
                        worker.removeEventListener('message', handler);
                        resolve();
                    }
                };
                worker.addEventListener('message', handler);
            });
        }

        // Finalize workbook
        worker.postMessage({ type: 'FINALIZE_WORKBOOK' });
    },

    calculateOptimalChunkSize(rowCount, columnCount) {
        // Calculate chunk size based on available memory and data complexity
        const baseChunkSize = 1000;
        const memoryFactor = columnCount > 50 ? 0.5 : columnCount > 20 ? 0.7 : 1;
        const sizeFactor = rowCount > 100000 ? 0.5 : rowCount > 50000 ? 0.7 : 1;

        return Math.max(100, Math.floor(baseChunkSize * memoryFactor * sizeFactor));
    },

    async setupWorksheetStyles(worksheet, dates, headerItems, title, subTitle) {
        const headerStyle = {
            font: { bold: true, color: { argb: 'FFFFFF' }, size: 11 },
            fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: '002060' } },
            alignment: { horizontal: 'center', vertical: 'middle' },
        };

        const subHeaderStyle = {
            font: { bold: true, color: { argb: 'FFFFFF' }, size: 10 },
            fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: '4472C4' } },
            alignment: { horizontal: 'center', vertical: 'middle' },
        };

        // Title row
        const titleRow = worksheet.getRow(1);
        titleRow.height = 30;
        const titleCell = titleRow.getCell(1);
        titleCell.value = title;
        titleCell.style = headerStyle;
        worksheet.mergeCells(1, 1, 1, headerItems.length);

        // Subtitle row
        const subTitleRow = worksheet.getRow(2);
        subTitleRow.height = 25;
        const subTitleCell = subTitleRow.getCell(1);
        subTitleCell.value = subTitle;
        subTitleCell.style = subHeaderStyle;
        worksheet.mergeCells(2, 1, 2, headerItems.length);

        // Date headers (rows 4-14)
        if (dates && dates.length > 0) {
            dates.forEach((date, index) => {
                const row = worksheet.getRow(4 + index);
                row.height = 25;
                headerItems.forEach((header, colIndex) => {
                    const cell = row.getCell(1 + colIndex);
                    cell.value = date[header] || '';
                    cell.style = subHeaderStyle;
                });
            });
        }

        // Main headers row (row 15)
        const headerRow = worksheet.getRow(15);
        headerRow.height = 30;
        headerItems.forEach((header, index) => {
            const cell = headerRow.getCell(1 + index);
            cell.value = header;
            cell.style = headerStyle;
        });
    },

    async processDataChunk(worksheet, chunk, headerItems, title, startRowIndex) {
        return new Promise((resolve) => {
            const colorMapping = {
                green: '008000',
                red: 'FF0000',
                blue: '0000FF',
                black: '000000',
            };

            let processedInChunk = 0;

            const processRow = () => {
                if (processedInChunk >= chunk.length) {
                    resolve();
                    return;
                }

                const rowData = chunk[processedInChunk];
                const row = worksheet.getRow(startRowIndex + processedInChunk);
                row.height = 25;

                headerItems.forEach((header, colIndex) => {
                    const cell = row.getCell(1 + colIndex);
                    cell.value = rowData[header];
                    cell.alignment = { horizontal: 'center', vertical: 'middle' };
                    cell.font = { bold: true };

                    if (rowData.color) {
                        this.employeeColoring(header, rowData, cell, colorMapping);
                    }
                    if (title === 'Overall Visits Report') {
                        this.overallVisits(header, rowData, cell);
                    }
                    if (title === 'Employee Tracking Report') {
                        this.employeeTracking(cell);
                    }
                    if (title === 'Structure Report') {
                        this.structureData(header, cell);
                    }
                });

                processedInChunk++;

                // Use requestAnimationFrame to prevent UI blocking
                requestAnimationFrame(processRow);
            };

            processRow();
        });
    },

    finalizeWorksheet(worksheet) {
        worksheet.columns.forEach(column => {
            column.width = 20;
        });
    },

    getMemoryUsage() {
        if (performance.memory) {
            return performance.memory.usedJSHeapSize;
        }
        return 0;
    },

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    },

    employeeColoring(header, rowData, cell, colorMapping) {
        if (header === 'employee' || header === 'division') {
            const cellColor = colorMapping[rowData.color.toLowerCase()]; // Default to white if no color found
            cell.font = {
                bold: true,
                color: { argb: cellColor },
            };
        }
    },
    structureData(header, cell) {
        if (header === 'brick_name' || header === 'brick_id') {
            cell.font = {
                bold: true,
                color: { argb: '960000' },
            };
        }
    },
    overallVisits(header, rowData, cell) {
        if (header === 'division') {
            cell.font = {
                bold: true,
                color: { argb: '008000' },
            };
        }
        if (header === 'account' || header === 'doctor') {
            cell.font = {
                bold: true,
                color: { argb: '0000ff' },
            };
        }
        if (header === 'acc_type' || header === 'shift') {
            let shiftColor = '000000';
            if (rowData.acc_shift_id === 1) {
                shiftColor = 'EFA609';
            }
            if (rowData.acc_shift_id === 2) {
                shiftColor = 'EF09C2';
            }
            if (rowData.acc_shift_id === 3) {
                shiftColor = '09EFDE';
            }
            cell.font = {
                bold: true,
                color: { argb: shiftColor },
            };
        }
    },

    showExcelProcessingState(isProcessing, message = '', fileInfo = null) {
        // Set processing state in component data if available
        if (this.$data) {
            this.$set(this.$data, 'isExcelProcessing', isProcessing);
            this.$set(this.$data, 'excelProcessingMessage', message);
            this.$set(this.$data, 'excelProgress', 0);
            this.$set(this.$data, 'excelStartTime', isProcessing ? Date.now() : null);
            this.$set(this.$data, 'excelEstimatedTime', '');
            this.$set(this.$data, 'excelMemoryUsage', '');

            // Set file tracking information
            if (fileInfo) {
                this.$set(this.$data, 'excelCurrentFile', fileInfo.currentFile || 0);
                this.$set(this.$data, 'excelTotalFiles', fileInfo.totalFiles || 1);
            } else {
                this.$set(this.$data, 'excelCurrentFile', 0);
                this.$set(this.$data, 'excelTotalFiles', 1);
            }
        }

        // Emit event for parent components to handle
        this.$emit('excel-processing-state', {
            isProcessing,
            message,
            startTime: isProcessing ? Date.now() : null,
            fileInfo
        });
    },

    updateExcelProgress(progress, message = '', fileInfo = null) {
        const estimatedTime = this.calculateEstimatedTime(progress);
        const memoryUsage = this.formatMemoryUsage(this.getMemoryUsage());

        if (this.$data) {
            this.$set(this.$data, 'excelProgress', progress);
            this.$set(this.$data, 'excelProcessingMessage', message);
            this.$set(this.$data, 'excelEstimatedTime', estimatedTime);
            this.$set(this.$data, 'excelMemoryUsage', memoryUsage);

            // Update file tracking information if provided
            if (fileInfo) {
                this.$set(this.$data, 'excelCurrentFile', fileInfo.currentFile || 0);
                this.$set(this.$data, 'excelTotalFiles', fileInfo.totalFiles || 1);
            }
        }

        this.$emit('excel-progress', {
            progress,
            message,
            estimatedTime,
            memoryUsage,
            fileInfo
        });
    },

    calculateEstimatedTime(progress) {
        if (!this.$data || !this.$data.excelStartTime || progress <= 0) {
            return '';
        }

        const elapsed = Date.now() - this.$data.excelStartTime;
        const rate = progress / elapsed; // progress per ms
        const remaining = (100 - progress) / rate; // ms remaining

        if (remaining < 60000) { // Less than 1 minute
            return `${Math.round(remaining / 1000)} seconds`;
        } else if (remaining < 3600000) { // Less than 1 hour
            return `${Math.round(remaining / 60000)} minutes`;
        } else {
            const hours = Math.floor(remaining / 3600000);
            const minutes = Math.round((remaining % 3600000) / 60000);
            return `${hours}h ${minutes}m`;
        }
    },

    formatMemoryUsage(bytes) {
        if (!bytes) return '';
        const mb = bytes / (1024 * 1024);
        if (mb < 1024) {
            return `${mb.toFixed(1)} MB`;
        } else {
            return `${(mb / 1024).toFixed(1)} GB`;
        }
    },

    handleExcelError(error, itemCount) {
        console.error('Excel generation error:', error);

        // Log detailed error information for debugging
        this.logExcelError(error, itemCount);

        const errorMessage = this.getExcelErrorMessage(error, itemCount);

        // Format error object properly for showErrorMessage
        const formattedError = this.formatErrorForShowErrorMessage(errorMessage);
        this.showErrorMessage(formattedError);

        // Suggest alternatives for very large datasets
        if (itemCount > 100000) {
            this.suggestAlternatives(itemCount);
        }

        // Attempt fallback if appropriate
        this.attemptExcelFallback(error, itemCount);
    },

    formatErrorForShowErrorMessage(message) {
        // showErrorMessage expects an error object with response.data.message structure
        // or a TypeError for generic handling
        if (typeof message === 'string') {
            return {
                response: {
                    data: {
                        message: message
                    }
                }
            };
        }

        // If it's already an error object, check if it has the right structure
        if (message && message.response && message.response.data) {
            return message;
        }

        // If it's a generic Error object
        if (message instanceof Error) {
            return {
                response: {
                    data: {
                        message: message.message || 'An unexpected error occurred during Excel generation'
                    }
                }
            };
        }

        // Fallback for any other type
        return {
            response: {
                data: {
                    message: String(message) || 'An unexpected error occurred during Excel generation'
                }
            }
        };
    },

    logExcelError(error, itemCount) {
        const memoryInfo = this.getMemoryUsage();
        const errorInfo = {
            message: error.message,
            name: error.name,
            stack: error.stack,
            itemCount,
            memoryUsage: memoryInfo,
            memoryUsageMB: memoryInfo ? Math.round(memoryInfo / 1024 / 1024) : 'Unknown',
            userAgent: navigator.userAgent,
            timestamp: new Date().toISOString(),
            url: window.location.href,
            browserInfo: {
                language: navigator.language,
                userAgent: navigator.userAgent,
                cookieEnabled: navigator.cookieEnabled,
                onLine: navigator.onLine
            },
            performanceInfo: {
                memory: performance.memory ? {
                    usedJSHeapSize: performance.memory.usedJSHeapSize,
                    totalJSHeapSize: performance.memory.totalJSHeapSize,
                    jsHeapSizeLimit: performance.memory.jsHeapSizeLimit
                } : null,
                now: performance.now ? performance.now() : null
            }
        };

        console.group('🚨 Excel Generation Error');
        console.error('Error Details:', errorInfo);
        console.error('Original Error:', error);

        // Additional debugging information
        if (itemCount > 100000) {
            console.warn(`Large dataset detected: ${itemCount.toLocaleString()} records`);
        }

        if (memoryInfo && memoryInfo > 1024 * 1024 * 1024) { // > 1GB
            console.warn(`High memory usage: ${Math.round(memoryInfo / 1024 / 1024)}MB`);
        }

        console.groupEnd();

        // Store error in localStorage for debugging
        try {
            const errorLog = JSON.parse(localStorage.getItem('excel-error-log') || '[]');
            errorLog.push(errorInfo);

            // Keep only last 10 errors
            if (errorLog.length > 10) {
                errorLog.splice(0, errorLog.length - 10);
            }

            localStorage.setItem('excel-error-log', JSON.stringify(errorLog));
        } catch (storageError) {
            console.warn('Could not store error log:', storageError);
        }

        // Send error to monitoring service if available
        if (this.$sentry) {
            this.$sentry.captureException(error, {
                tags: {
                    feature: 'excel_export',
                    dataset_size: itemCount > 100000 ? 'large' : itemCount > 10000 ? 'medium' : 'small'
                },
                extra: errorInfo
            });
        }

        // Send to custom error tracking if available
        if (typeof window.trackError === 'function') {
            window.trackError('excel_generation_error', errorInfo);
        }
    },

    // Debug helper methods
    enableExcelDebugMode() {
        localStorage.setItem('excel-debug-mode', 'true');
        console.log('Excel debug mode enabled');
    },

    disableExcelDebugMode() {
        localStorage.removeItem('excel-debug-mode');
        console.log('Excel debug mode disabled');
    },

    isExcelDebugMode() {
        return localStorage.getItem('excel-debug-mode') === 'true';
    },

    debugLog(message, data = null) {
        if (this.isExcelDebugMode()) {
            console.log(`[Excel Debug] ${message}`, data);
        }
    },

    getExcelErrorLog() {
        try {
            return JSON.parse(localStorage.getItem('excel-error-log') || '[]');
        } catch (error) {
            console.warn('Could not retrieve error log:', error);
            return [];
        }
    },

    clearExcelErrorLog() {
        localStorage.removeItem('excel-error-log');
        console.log('Excel error log cleared');
    },

    getExcelErrorMessage(error, itemCount) {
        // Memory-related errors
        if (error.name === 'RangeError' || error.message.includes('memory') || error.message.includes('heap')) {
            return `Dataset too large for browser processing (${itemCount.toLocaleString()} records). The browser ran out of memory. Please try filtering the data or use server-side export.`;
        }

        // Quota exceeded errors
        if (error.message.includes('quota') || error.message.includes('storage')) {
            return 'Browser storage quota exceeded. Please clear browser cache and try again with a smaller dataset.';
        }

        // Web Worker errors
        if (error.message.includes('Worker')) {
            return 'Web Worker processing failed. Falling back to standard processing. Please try again or use a smaller dataset.';
        }

        // Network or file system errors
        if (error.message.includes('network') || error.message.includes('fetch')) {
            return 'Network error during Excel generation. Please check your connection and try again.';
        }

        // Browser compatibility errors
        if (error.message.includes('not supported')) {
            return 'Your browser does not support this Excel generation feature. Please try using a modern browser or contact support.';
        }

        // Generic error with helpful context
        return `Excel generation failed: ${error.message}. Dataset size: ${itemCount.toLocaleString()} records. Please try with a smaller dataset or contact support.`;
    },

    attemptExcelFallback(error, itemCount) {
        // Don't attempt fallback for extremely large datasets
        if (itemCount > 500000) {
            this.suggestServerSideExport(itemCount);
            return;
        }

        // For memory errors, suggest CSV export as fallback
        if (error.name === 'RangeError' || error.message.includes('memory')) {
            this.suggestCsvFallback(itemCount);
        }

        // For Web Worker errors, suggest standard processing
        if (error.message.includes('Worker') && itemCount < 100000) {
            this.suggestStandardProcessing();
        }
    },

    suggestAlternatives(itemCount) {
        const suggestions = [
            'Apply date range filters to reduce the dataset',
            'Use division or employee filters to limit records',
            'Export data in smaller batches (e.g., monthly)',
            'Consider CSV export for large datasets',
            'Contact support for server-side Excel generation'
        ];

        this.flash(`Large dataset detected (${itemCount.toLocaleString()} records). Suggestions: ${suggestions.join('; ')}`, 'warning');
    },

    suggestServerSideExport(itemCount) {
        this.flash(`Dataset extremely large (${itemCount.toLocaleString()} records). Server-side export recommended. Please contact support for assistance.`, 'info');
    },

    suggestCsvFallback(itemCount) {
        this.flash(`Memory limit reached with ${itemCount.toLocaleString()} records. Try CSV export instead, which uses less memory.`, 'info');
    },

    suggestStandardProcessing() {
        this.flash('Web Worker processing failed. You can try the standard export method, but it may be slower.', 'info');
    },

    // Memory monitoring and cleanup
    monitorMemoryUsage() {
        if (!performance.memory) return null;

        const memory = performance.memory;
        const usedMB = memory.usedJSHeapSize / (1024 * 1024);
        const limitMB = memory.jsHeapSizeLimit / (1024 * 1024);
        const usagePercent = (usedMB / limitMB) * 100;

        return {
            used: usedMB,
            limit: limitMB,
            percentage: usagePercent,
            critical: usagePercent > 80
        };
    },

    async performMemoryCleanup() {
        // Force garbage collection if available
        if (window.gc) {
            window.gc();
        }

        // Clear any large temporary variables
        if (this.$data) {
            this.$data.tempExcelData = null;
            this.$data.processedChunks = null;
        }

        // Wait for cleanup to complete
        await this.delay(100);
    },

    showSuccessMessage(message) {
        this.flash(message, 'success');
    },

    employeeTracking(cell) {

        if (cell.value && typeof cell.value === 'string') {
            const cellValue = cell.value.toLowerCase(); // Convert cell value to lowercase for comparison            // Conditional formatting based on cell value
            if (cellValue.includes('visits')) {
                cell.font = { bold: true, color: { argb: '0000FF' } }; // Blue for visits
            }
            if (cellValue.includes('ow')) {
                cell.font = { bold: true, color: { argb: '00FF00' } }; // Green for ow
            }
            if (cellValue.includes('off day')) {
                cell.font = { bold: true, color: { argb: 'FFA500' } }; // Orange for Off Day
            }
            if (cellValue.includes('public holiday')) {
                cell.font = { bold: true, color: { argb: 'FF00FF' } }; // Magenta for Public Holiday
            }
            if (cellValue.includes('no show')) {
                cell.font = { bold: true, color: { argb: 'f70000' } }; // Red for Vacation
            }
            if (cellValue.includes('vacation')) {
                cell.font = { bold: true, color: { argb: 'A52A2A' } }; // Orange for Off Day
            }
        }

    }
}
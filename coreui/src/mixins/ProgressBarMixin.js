/**
 * Enhanced Progress Bar Mixin
 * Provides optimized progress bar functionality for Vue components
 */

export default {
  data() {
    return {
      // Progress tracking
      uploadPercentage: 0,
      progressBar: false,
      
      // Request tracking
      _activeRequests: new Set(),
      _progressUnsubscribers: [],
      
      // Performance optimization
      _progressUpdateThrottle: null,
      _lastProgressUpdate: 0
    };
  },

  methods: {
    /**
     * Enhanced file import with optimized progress tracking
     * @param {string} path - API endpoint
     * @param {FormData} formData - Form data to upload
     * @param {Object} options - Additional options
     */
    async importFile(path, formData, options = {}) {
      const requestId = `upload_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      try {
        // Show progress bar
        this.progressBar = true;
        this.uploadPercentage = 0;
        
        // Track request
        this._activeRequests.add(requestId);
        
        // Setup progress tracking
        const progressHandler = this.createProgressHandler(requestId);
        
        const response = await axios.post(path, formData, {
          headers: {
            "content-type": "multipart/form-data",
          },
          onUploadProgress: progressHandler,
          // Add request timeout
          timeout: options.timeout || 300000, // 5 minutes default
          // Add request ID for tracking
          requestId
        });

        // Success handling
        this.handleUploadSuccess(response, options);
        return response;

      } catch (error) {
        // Error handling
        this.handleUploadError(error, options);
        throw error;
      } finally {
        // Cleanup
        this.cleanupRequest(requestId);
      }
    },

    /**
     * Create optimized progress handler with throttling
     */
    createProgressHandler(requestId) {
      return (progressEvent) => {
        const now = Date.now();
        
        // Throttle progress updates to avoid excessive re-renders
        if (now - this._lastProgressUpdate < 16) { // ~60fps
          if (this._progressUpdateThrottle) {
            clearTimeout(this._progressUpdateThrottle);
          }
          
          this._progressUpdateThrottle = setTimeout(() => {
            this.updateProgress(progressEvent, requestId);
          }, 16);
        } else {
          this.updateProgress(progressEvent, requestId);
        }
      };
    },

    /**
     * Update progress with optimization
     */
    updateProgress(progressEvent, requestId) {
      this._lastProgressUpdate = Date.now();
      
      const percentage = Math.round((progressEvent.loaded * 100) / progressEvent.total);
      
      // Only update if percentage actually changed
      if (this.uploadPercentage !== percentage) {
        this.uploadPercentage = percentage;
      }
      
      // Update global progress service if available
      if (window.ProgressBarService) {
        window.ProgressBarService.addRequest(requestId, {
          loaded: progressEvent.loaded,
          total: progressEvent.total,
          progress: percentage
        });
      }
    },

    /**
     * Handle successful upload
     */
    handleUploadSuccess(response, options = {}) {
      // Hide modals
      if (this.successModal !== undefined) this.successModal = false;
      if (this.updateModal !== undefined) this.updateModal = false;
      
      // Show success message
      const message = options.successMessage || "File Uploaded Successfully";
      if (this.flash) {
        this.flash(message);
      }
      
      // Refresh data if method exists
      if (this.hasOwnProperty('getData') && typeof this.getData === 'function') {
        this.getData();
      }
      
      // Custom success callback
      if (options.onSuccess && typeof options.onSuccess === 'function') {
        options.onSuccess(response);
      }
    },

    /**
     * Handle upload error with proper error reporting
     */
    handleUploadError(error, options = {}) {
      console.error('Upload error:', error);
      
      // Show error message
      let errorMessage = options.errorMessage || "Upload failed. Please try again.";
      
      if (error.response) {
        // Server responded with error status
        const status = error.response.status;
        const data = error.response.data;
        
        if (status === 413) {
          errorMessage = "File too large. Please choose a smaller file.";
        } else if (status === 422 && data.errors) {
          errorMessage = Object.values(data.errors).flat().join(', ');
        } else if (data.message) {
          errorMessage = data.message;
        }
      } else if (error.code === 'ECONNABORTED') {
        errorMessage = "Upload timeout. Please try again.";
      } else if (!error.response) {
        errorMessage = "Network error. Please check your connection.";
      }
      
      if (this.flash) {
        this.flash(errorMessage, 'error');
      }
      
      // Custom error callback
      if (options.onError && typeof options.onError === 'function') {
        options.onError(error);
      }
    },

    /**
     * Cleanup request tracking
     */
    cleanupRequest(requestId) {
      this._activeRequests.delete(requestId);
      
      // Hide progress bar if no active requests
      if (this._activeRequests.size === 0) {
        setTimeout(() => {
          if (this._activeRequests.size === 0) {
            this.progressBar = false;
            this.uploadPercentage = 0;
          }
        }, 500); // Small delay for better UX
      }
      
      // Clear throttle timeout
      if (this._progressUpdateThrottle) {
        clearTimeout(this._progressUpdateThrottle);
        this._progressUpdateThrottle = null;
      }
      
      // Remove from global progress service
      if (window.ProgressBarService) {
        window.ProgressBarService.removeRequest(requestId);
      }
    },

    /**
     * Subscribe to global progress events
     */
    subscribeToProgress() {
      if (!window.ProgressBarService) return;
      
      // Subscribe to progress updates
      const progressUnsub = window.ProgressBarService.onProgress((data) => {
        // Handle global progress updates if needed
        this.handleGlobalProgress(data);
      });
      
      // Subscribe to errors
      const errorUnsub = window.ProgressBarService.onError((error) => {
        this.handleGlobalError(error);
      });
      
      // Store unsubscribers for cleanup
      this._progressUnsubscribers.push(progressUnsub, errorUnsub);
    },

    /**
     * Handle global progress updates
     */
    handleGlobalProgress(data) {
      // Override in components if needed
      // This can be used for coordinating multiple progress indicators
    },

    /**
     * Handle global errors
     */
    handleGlobalError(error) {
      // Override in components if needed
      console.warn('Global request error:', error);
    },

    /**
     * Cancel all active requests for this component
     */
    cancelActiveRequests() {
      // Note: This would require storing cancel tokens
      // Implementation depends on specific requirements
      this._activeRequests.clear();
      this.progressBar = false;
      this.uploadPercentage = 0;
    }
  },

  mounted() {
    // Subscribe to global progress events
    this.subscribeToProgress();
  },

  beforeDestroy() {
    // Cleanup all subscriptions
    this._progressUnsubscribers.forEach(unsub => {
      try {
        unsub();
      } catch (error) {
        console.error('Error unsubscribing from progress events:', error);
      }
    });
    this._progressUnsubscribers = [];
    
    // Clear any pending timeouts
    if (this._progressUpdateThrottle) {
      clearTimeout(this._progressUpdateThrottle);
    }
    
    // Cancel active requests
    this.cancelActiveRequests();
  }
};

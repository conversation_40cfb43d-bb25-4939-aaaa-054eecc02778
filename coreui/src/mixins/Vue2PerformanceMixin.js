/**
 * Vue 2 Performance Mixin
 * Provides v-memo-like functionality for Vue 2 applications
 * This mixin offers performance optimizations similar to Vue 3's v-memo directive
 */

export default {
  data() {
    return {
      // Performance tracking
      _performanceCache: new Map(),
      _renderThrottles: new Map(),
      _lastRenderTimes: new Map(),
    };
  },

  methods: {
    /**
     * Vue 2 compatible memo function
     * Mimics v-memo behavior by caching render results based on dependencies
     * @param {string} key - Unique key for the cached content
     * @param {Array} dependencies - Array of values to watch for changes
     * @param {Function} renderFn - Function that returns the content to render
     * @returns {*} - Cached or newly rendered content
     */
    $memo(key, dependencies, renderFn) {
      const depsKey = JSON.stringify(dependencies);
      const cached = this._performanceCache.get(key);
      
      // Check if we have cached content and dependencies haven't changed
      if (cached && cached.depsKey === depsKey) {
        return cached.content;
      }
      
      // Dependencies changed or no cache, render new content
      const content = renderFn();
      
      // Cache the new content
      this._performanceCache.set(key, {
        content,
        depsKey,
        timestamp: Date.now()
      });
      
      return content;
    },

    /**
     * Throttled render function to prevent excessive re-renders
     * @param {string} key - Unique key for the throttle
     * @param {Function} renderFn - Function to execute
     * @param {number} delay - Throttle delay in milliseconds (default: 16ms for 60fps)
     */
    $throttledRender(key, renderFn, delay = 16) {
      const now = Date.now();
      const lastRender = this._lastRenderTimes.get(key) || 0;
      
      // If enough time has passed, execute immediately
      if (now - lastRender >= delay) {
        this._lastRenderTimes.set(key, now);
        return renderFn();
      }
      
      // Otherwise, throttle the execution
      if (this._renderThrottles.has(key)) {
        clearTimeout(this._renderThrottles.get(key));
      }
      
      const timeoutId = setTimeout(() => {
        this._lastRenderTimes.set(key, Date.now());
        this._renderThrottles.delete(key);
        renderFn();
      }, delay - (now - lastRender));
      
      this._renderThrottles.set(key, timeoutId);
    },

    /**
     * Conditional rendering helper
     * Only re-renders when dependencies actually change
     * @param {string} key - Unique key for tracking
     * @param {*} currentValue - Current value to compare
     * @param {Function} renderFn - Function to execute if value changed
     */
    $conditionalRender(key, currentValue, renderFn) {
      const cached = this._performanceCache.get(key);
      
      if (!cached || cached.value !== currentValue) {
        const result = renderFn();
        this._performanceCache.set(key, {
          value: currentValue,
          result,
          timestamp: Date.now()
        });
        return result;
      }
      
      return cached.result;
    },

    /**
     * Batch DOM updates to improve performance
     * @param {Function} updateFn - Function containing DOM updates
     */
    $batchUpdate(updateFn) {
      this.$nextTick(() => {
        updateFn();
      });
    },

    /**
     * Debounced update function
     * @param {string} key - Unique key for the debounce
     * @param {Function} updateFn - Function to execute
     * @param {number} delay - Debounce delay in milliseconds
     */
    $debouncedUpdate(key, updateFn, delay = 100) {
      if (this._renderThrottles.has(key)) {
        clearTimeout(this._renderThrottles.get(key));
      }
      
      const timeoutId = setTimeout(() => {
        this._renderThrottles.delete(key);
        updateFn();
      }, delay);
      
      this._renderThrottles.set(key, timeoutId);
    },

    /**
     * Check if dependencies have changed
     * @param {string} key - Cache key
     * @param {Array} dependencies - Array of dependencies to check
     * @returns {boolean} - True if dependencies changed
     */
    $dependenciesChanged(key, dependencies) {
      const depsKey = JSON.stringify(dependencies);
      const cached = this._performanceCache.get(key);
      
      if (!cached || cached.depsKey !== depsKey) {
        this._performanceCache.set(key, { depsKey });
        return true;
      }
      
      return false;
    },

    /**
     * Clear performance cache for specific key or all keys
     * @param {string} key - Optional key to clear, if not provided clears all
     */
    $clearPerformanceCache(key = null) {
      if (key) {
        this._performanceCache.delete(key);
      } else {
        this._performanceCache.clear();
      }
    },

    /**
     * Get performance statistics
     * @returns {Object} - Performance statistics
     */
    $getPerformanceStats() {
      return {
        cacheSize: this._performanceCache.size,
        activeThrottles: this._renderThrottles.size,
        cacheKeys: Array.from(this._performanceCache.keys()),
        throttleKeys: Array.from(this._renderThrottles.keys())
      };
    }
  },

  beforeDestroy() {
    // Cleanup all throttles and timeouts
    this._renderThrottles.forEach(timeoutId => {
      clearTimeout(timeoutId);
    });
    
    // Clear all caches
    this._performanceCache.clear();
    this._renderThrottles.clear();
    this._lastRenderTimes.clear();
  },

  created() {
    // Initialize performance tracking
    if (process.env.NODE_ENV === 'development') {
      // Add performance monitoring in development
      this.$watch(() => this._performanceCache.size, (newSize) => {
        if (newSize > 100) {
          console.warn(`Performance cache size is large (${newSize}). Consider clearing unused cache entries.`);
        }
      });
    }
  }
};

/**
 * Usage Examples:
 * 
 * 1. In template (using computed properties):
 * <div v-if="memoizedHeader" v-html="memoizedHeader"></div>
 * 
 * computed: {
 *   memoizedHeader() {
 *     return this.$memo('header', [this.title, this.subtitle], () => {
 *       return `<h1>${this.title}</h1><p>${this.subtitle}</p>`;
 *     });
 *   }
 * }
 * 
 * 2. In methods:
 * methods: {
 *   updateProgress() {
 *     this.$throttledRender('progress', () => {
 *       // Update progress bar
 *       this.progressValue = newValue;
 *     }, 16); // 60fps
 *   }
 * }
 * 
 * 3. Conditional rendering:
 * computed: {
 *   expensiveComputation() {
 *     return this.$conditionalRender('computation', this.inputValue, () => {
 *       return this.performExpensiveCalculation();
 *     });
 *   }
 * }
 */

import ProgressBarMixin from './ProgressBarMixin';

export default {
    // Include the enhanced progress bar mixin
    mixins: [ProgressBarMixin],

    methods: {
        /**
         * Enhanced importFile method with better error handling and performance
         * @param {string} path
         * @param {FormData} formData
         * @param {Object} options - Additional options
         */
        async importFileEnhanced(path, formData, options = {}) {
            const requestId = `import_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

            try {
                // Show progress bar
                this.progressBar = true;
                this.uploadPercentage = 0;

                // Track request
                this._activeRequests.add(requestId);

                const response = await axios.post(path, formData, {
                    headers: {
                        "content-type": "multipart/form-data",
                    },
                    onUploadProgress: (progressEvent) => {
                        const percentage = Math.round((progressEvent.loaded * 100) / progressEvent.total);

                        // Throttle updates for better performance
                        if (percentage !== this.uploadPercentage) {
                            this.uploadPercentage = percentage;
                        }
                    },
                    timeout: options.timeout || 300000, // 5 minutes default
                    requestId
                });

                // Success handling
                this.successModal = false;
                this.updateModal = false;
                this.flash(options.successMessage || "File Uploaded Successfully");

                if (this.hasOwnProperty('getData') && typeof this.getData === 'function') {
                    this.getData();
                }

                return response;

            } catch (error) {
                // Enhanced error handling
                this.successModal = false;
                this.updateModal = false;

                if (error.response?.data?.message) {
                    this.showErrorMessage(error);
                } else {
                    // Fallback error handling
                    const errorMessage = this.getErrorMessage(error);
                    if (this.flash) {
                        this.flash(errorMessage, 'error');
                    }
                }

                if (error.response?.data?.failures) {
                    this.showExcelValidationFailures(error);
                }

                throw error;
            } finally {
                // Cleanup
                this._activeRequests.delete(requestId);

                if (this._activeRequests.size === 0) {
                    setTimeout(() => {
                        if (this._activeRequests.size === 0) {
                            this.progressBar = false;
                            this.uploadPercentage = 0;
                        }
                    }, 500);
                }
            }
        },

        /**
         * Legacy importFile method - maintained for backward compatibility
         * @param {string} path
         * @param {FormData} formData
         */
        importFile(path, formData) {
            return axios
                .post(path, formData, {
                    headers: {
                        "content-type": "multipart/form-data",
                    },
                    onUploadProgress: function(progressEvent) {
                        this.uploadPercentage = parseInt(
                            Math.round((progressEvent.loaded * 100) / progressEvent.total)
                        );
                    }.bind(this),
                })
                .then((response) => {
                    this.successModal = false;
                    this.updateModal = false;
                    this.progressBar = false;
                    this.flash("File Uploaded Successfully");
                    if (this.hasOwnProperty('getData')) this.getData()
                })
                .catch((error) => {
                    this.progressBar = false;
                    this.successModal = false;
                    this.updateModal = false;
                    if (error.response?.data?.message) {
                        this.showErrorMessage(error)
                    }
                    if (error.response?.data?.failures) {
                        this.showExcelValidationFailures(error)
                    }
                })
        },

        /**
         * Get user-friendly error message
         */
        getErrorMessage(error) {
            if (error.response) {
                const status = error.response.status;
                const data = error.response.data;

                if (status === 413) {
                    return "File too large. Please choose a smaller file.";
                } else if (status === 422 && data.errors) {
                    return Object.values(data.errors).flat().join(', ');
                } else if (data.message) {
                    return data.message;
                }
            } else if (error.code === 'ECONNABORTED') {
                return "Upload timeout. Please try again.";
            } else if (!error.response) {
                return "Network error. Please check your connection.";
            }

            return "Upload failed. Please try again.";
        }
    }
}
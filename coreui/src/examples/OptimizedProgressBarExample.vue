<template>
  <div class="progress-example">
    <h2>Optimized Progress Bar Examples</h2>
    
    <!-- Basic Upload Example -->
    <div class="example-section">
      <h3>File Upload with Progress</h3>
      <input 
        type="file" 
        @change="handleFileSelect" 
        ref="fileInput"
        accept=".csv,.xlsx,.xls"
      />
      <button 
        @click="uploadFile" 
        :disabled="!selectedFile || isUploading"
        class="btn btn-primary"
      >
        {{ isUploading ? 'Uploading...' : 'Upload File' }}
      </button>
      
      <!-- Progress Display -->
      <div v-if="isUploading" class="progress-display">
        <div class="progress-bar">
          <div 
            class="progress-fill" 
            :style="{ width: uploadPercentage + '%' }"
          ></div>
        </div>
        <span>{{ uploadPercentage }}%</span>
      </div>
    </div>

    <!-- Multiple Requests Example -->
    <div class="example-section">
      <h3>Multiple Concurrent Requests</h3>
      <button 
        @click="startMultipleRequests" 
        :disabled="hasActiveRequests"
        class="btn btn-secondary"
      >
        Start Multiple Downloads
      </button>
      
      <div v-if="requestStats.activeCount > 0" class="stats-display">
        <p>Active Requests: {{ requestStats.activeCount }}</p>
        <p>Overall Progress: {{ requestStats.totalProgress }}%</p>
        <p>Average Duration: {{ Math.round(requestStats.averageDuration / 1000) }}s</p>
      </div>
    </div>

    <!-- Error Handling Example -->
    <div class="example-section">
      <h3>Error Handling</h3>
      <button 
        @click="simulateError" 
        class="btn btn-danger"
      >
        Simulate Upload Error
      </button>
      
      <div v-if="lastError" class="error-display">
        <strong>Last Error:</strong> {{ lastError }}
      </div>
    </div>

    <!-- Progress Events Log -->
    <div class="example-section">
      <h3>Progress Events Log</h3>
      <div class="events-log">
        <div 
          v-for="(event, index) in progressEvents.slice(-10)" 
          :key="index"
          class="event-item"
        >
          <span class="timestamp">{{ formatTime(event.timestamp) }}</span>
          <span class="event-type">{{ event.type }}</span>
          <span class="event-data">{{ event.data }}</span>
        </div>
      </div>
      <button @click="clearEvents" class="btn btn-sm btn-outline-secondary">
        Clear Log
      </button>
    </div>
  </div>
</template>

<script>
import ProgressBarMixin from '../mixins/ProgressBarMixin';

export default {
  name: 'OptimizedProgressBarExample',
  mixins: [ProgressBarMixin],
  
  data() {
    return {
      selectedFile: null,
      isUploading: false,
      hasActiveRequests: false,
      lastError: null,
      progressEvents: [],
      requestStats: {
        activeCount: 0,
        totalProgress: 0,
        averageDuration: 0
      },
      // Unsubscribe functions
      progressUnsubscribe: null,
      errorUnsubscribe: null,
      completionUnsubscribe: null
    };
  },

  methods: {
    handleFileSelect(event) {
      this.selectedFile = event.target.files[0];
    },

    async uploadFile() {
      if (!this.selectedFile) return;

      this.isUploading = true;
      this.lastError = null;

      try {
        const formData = new FormData();
        formData.append('file', this.selectedFile);

        // Use the enhanced import method from mixin
        await this.importFileEnhanced('/api/upload', formData, {
          successMessage: 'File uploaded successfully!',
          timeout: 60000, // 1 minute timeout
          onSuccess: (response) => {
            this.logEvent('success', `Upload completed: ${response.data.message || 'Success'}`);
          },
          onError: (error) => {
            this.lastError = this.getErrorMessage(error);
            this.logEvent('error', this.lastError);
          }
        });

      } catch (error) {
        this.lastError = this.getErrorMessage(error);
        this.logEvent('error', this.lastError);
      } finally {
        this.isUploading = false;
        // Clear file input
        this.$refs.fileInput.value = '';
        this.selectedFile = null;
      }
    },

    async startMultipleRequests() {
      this.hasActiveRequests = true;
      
      // Simulate multiple concurrent requests
      const requests = [
        axios.get('/api/data/large-dataset-1'),
        axios.get('/api/data/large-dataset-2'),
        axios.get('/api/data/large-dataset-3')
      ];

      try {
        this.logEvent('info', 'Starting multiple concurrent requests');
        await Promise.all(requests);
        this.logEvent('success', 'All requests completed successfully');
      } catch (error) {
        this.logEvent('error', `Request failed: ${error.message}`);
      } finally {
        this.hasActiveRequests = false;
      }
    },

    simulateError() {
      // Simulate a failed upload
      const formData = new FormData();
      formData.append('file', new Blob(['invalid data'], { type: 'text/plain' }));

      this.importFileEnhanced('/api/upload/invalid', formData, {
        timeout: 5000
      }).catch(error => {
        // Error is already handled by the mixin
        this.logEvent('error', 'Simulated error completed');
      });
    },

    logEvent(type, data) {
      this.progressEvents.push({
        timestamp: Date.now(),
        type,
        data
      });
    },

    clearEvents() {
      this.progressEvents = [];
    },

    formatTime(timestamp) {
      return new Date(timestamp).toLocaleTimeString();
    },

    // Override mixin methods for custom handling
    handleGlobalProgress(data) {
      this.logEvent('progress', `Request ${data.requestId}: ${data.progress}%`);
      this.updateRequestStats();
    },

    handleGlobalError(error) {
      this.logEvent('error', `Global error: ${error.message}`);
      this.updateRequestStats();
    },

    updateRequestStats() {
      if (window.ProgressBarService) {
        this.requestStats = window.ProgressBarService.getStats();
      }
    }
  },

  mounted() {
    // Subscribe to global progress service events
    if (window.ProgressBarService) {
      this.progressUnsubscribe = window.ProgressBarService.onProgress((data) => {
        this.handleGlobalProgress(data);
      });

      this.errorUnsubscribe = window.ProgressBarService.onError((error) => {
        this.handleGlobalError(error);
      });

      this.completionUnsubscribe = window.ProgressBarService.onCompletion(() => {
        this.logEvent('info', 'All requests completed');
        this.hasActiveRequests = false;
        this.updateRequestStats();
      });
    }

    // Update stats periodically
    this.statsInterval = setInterval(() => {
      this.updateRequestStats();
    }, 1000);
  },

  beforeDestroy() {
    // Cleanup subscriptions
    if (this.progressUnsubscribe) this.progressUnsubscribe();
    if (this.errorUnsubscribe) this.errorUnsubscribe();
    if (this.completionUnsubscribe) this.completionUnsubscribe();
    
    // Clear interval
    if (this.statsInterval) {
      clearInterval(this.statsInterval);
    }
  }
};
</script>

<style scoped>
.progress-example {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.example-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.progress-display {
  margin-top: 10px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.progress-bar {
  flex: 1;
  height: 20px;
  background: #f0f0f0;
  border-radius: 10px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  transition: width 0.3s ease;
}

.stats-display {
  margin-top: 10px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 4px;
}

.error-display {
  margin-top: 10px;
  padding: 10px;
  background: #f8d7da;
  color: #721c24;
  border-radius: 4px;
}

.events-log {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #ddd;
  padding: 10px;
  margin-bottom: 10px;
  font-family: monospace;
  font-size: 12px;
}

.event-item {
  display: flex;
  gap: 10px;
  margin-bottom: 5px;
}

.timestamp {
  color: #666;
  min-width: 80px;
}

.event-type {
  font-weight: bold;
  min-width: 60px;
}

.event-type:contains('error') {
  color: #dc3545;
}

.event-type:contains('success') {
  color: #28a745;
}

.event-type:contains('progress') {
  color: #007bff;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin-right: 10px;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-danger {
  background: #dc3545;
  color: white;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
</style>

<template>
  <div v-if="isVisible" class="excel-processing-overlay">
    <div class="excel-processing-modal">
      <div class="excel-processing-header">
        <c-icon name="cib-experts-exchange" class="excel-icon" />
        <h4>Excel Processing</h4>
      </div>
      
      <div class="excel-processing-body">
        <div class="processing-message">
          {{ message || 'Processing Excel data...' }}
        </div>

        <div v-if="currentFile && totalFiles > 1" class="file-progress">
          <div class="file-indicator">
            <c-icon name="cil-file" class="file-icon" />
            <span>File {{ currentFile }} of {{ totalFiles }}</span>
          </div>
        </div>
        
        <div class="progress-container">
          <div class="progress-bar">
            <div 
              class="progress-fill" 
              :style="{ width: progress + '%' }"
              :class="{ 'progress-complete': progress >= 100 }"
            ></div>
          </div>
          <div class="progress-text">
            {{ Math.round(progress) }}%
          </div>
        </div>
        
        <div v-if="estimatedTime" class="estimated-time">
          Estimated time remaining: {{ estimatedTime }}
        </div>
        
        <div v-if="memoryUsage" class="memory-usage">
          Memory usage: {{ memoryUsage }}
        </div>
        
        <div class="processing-tips">
          <div class="tip-item">
            <c-icon name="cil-info" class="tip-icon" />
            <span>Large datasets may take several minutes to process</span>
          </div>
          <div class="tip-item">
            <c-icon name="cil-warning" class="tip-icon" />
            <span>Please don't close this tab during processing</span>
          </div>
        </div>
        
        <div v-if="showCancelButton" class="cancel-container">
          <c-button 
            color="secondary" 
            variant="outline" 
            @click="$emit('cancel')"
            :disabled="progress >= 90"
          >
            Cancel
          </c-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ExcelProcessingOverlay',
  props: {
    isVisible: {
      type: Boolean,
      default: false
    },
    progress: {
      type: Number,
      default: 0
    },
    message: {
      type: String,
      default: ''
    },
    estimatedTime: {
      type: String,
      default: ''
    },
    memoryUsage: {
      type: String,
      default: ''
    },
    showCancelButton: {
      type: Boolean,
      default: false
    },
    currentFile: {
      type: Number,
      default: 0
    },
    totalFiles: {
      type: Number,
      default: 1
    }
  },
  emits: ['cancel']
}
</script>

<style scoped>
.excel-processing-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  backdrop-filter: blur(2px);
}

.excel-processing-modal {
  background: white;
  border-radius: 12px;
  padding: 0;
  min-width: 400px;
  max-width: 500px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.excel-processing-header {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  padding: 20px;
  border-radius: 12px 12px 0 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.excel-icon {
  font-size: 24px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.excel-processing-header h4 {
  margin: 0;
  font-weight: 600;
  font-size: 18px;
}

.excel-processing-body {
  padding: 24px;
}

.processing-message {
  font-size: 16px;
  color: #495057;
  margin-bottom: 16px;
  text-align: center;
  font-weight: 500;
}

.file-progress {
  margin-bottom: 16px;
}

.file-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: #e3f2fd;
  border-radius: 6px;
  border-left: 4px solid #2196f3;
  font-size: 14px;
  color: #1976d2;
  font-weight: 600;
}

.file-icon {
  font-size: 16px;
  color: #2196f3;
}

.progress-container {
  margin-bottom: 16px;
}

.progress-bar {
  width: 100%;
  height: 12px;
  background-color: #e9ecef;
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 8px;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #28a745, #20c997);
  border-radius: 6px;
  transition: width 0.3s ease;
  position: relative;
  overflow: hidden;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.progress-complete {
  background: linear-gradient(90deg, #28a745, #20c997) !important;
}

.progress-text {
  text-align: center;
  font-weight: 600;
  color: #495057;
  font-size: 14px;
}

.estimated-time,
.memory-usage {
  text-align: center;
  font-size: 14px;
  color: #6c757d;
  margin-bottom: 8px;
}

.processing-tips {
  margin: 20px 0;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #17a2b8;
}

.tip-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 13px;
  color: #495057;
}

.tip-item:last-child {
  margin-bottom: 0;
}

.tip-icon {
  font-size: 14px;
  color: #17a2b8;
  flex-shrink: 0;
}

.cancel-container {
  text-align: center;
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #dee2e6;
}

/* Responsive design */
@media (max-width: 576px) {
  .excel-processing-modal {
    min-width: 320px;
    margin: 20px;
  }
  
  .excel-processing-header,
  .excel-processing-body {
    padding: 16px;
  }
}
</style>

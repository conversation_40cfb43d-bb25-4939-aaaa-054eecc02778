<template>
  <!-- Modern Progress Bar Overlay -->
  <div v-if="dialog" class="progress-overlay" :style="overlayStyle">
    <div class="progress-container">
      <!-- Animated Background -->
      <div class="progress-bg-animation"></div>

      <!-- Main Progress Card -->
      <div class="progress-card">
        <!-- Header with Icon -->
        <div class="progress-header" v-if="shouldRenderHeader" :key="headerCacheKey">
          <div class="progress-icon">
            <div class="icon-spinner">
              <svg viewBox="0 0 50 50" class="spinner-svg">
                <circle cx="25" cy="25" r="20" fill="none" stroke="url(#gradient)" stroke-width="3" stroke-linecap="round" stroke-dasharray="31.416" stroke-dashoffset="31.416">
                  <animate attributeName="stroke-array" dur="2s" values="0 31.416;15.708 15.708;0 31.416" repeatCount="indefinite"/>
                  <animate attributeName="stroke-dashoffset" dur="2s" values="0;-15.708;-31.416" repeatCount="indefinite"/>
                </circle>
                <defs>
                  <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
                  </linearGradient>
                </defs>
              </svg>
            </div>
          </div>
          <h4 class="progress-title">{{ displayTitle }}</h4>
          <p class="progress-subtitle">{{ displaySubtitle }}</p>
        </div>

        <!-- Progress Bar -->
        <div class="progress-bar-container">
          <div class="progress-bar-track">
            <div class="progress-bar-fill" :style="progressBarStyle">
              <div class="progress-bar-glow"></div>
            </div>
          </div>
          <div class="progress-percentage">{{ progressPercentage }}%</div>
        </div>

        <!-- Animated Dots -->
        <div class="progress-dots" v-if="shouldRenderDots" :key="dotsCacheKey">
          <div
            v-for="(_, index) in dots"
            :key="index"
            class="dot"
            :class="{ active: activeDots[index] }"
          ></div>
        </div>

        <!-- Status Message -->
        <div class="progress-status">
          <span class="status-text">{{ currentStatus }}</span>
        </div>
      </div>

      <!-- Floating Particles -->
      <div class="particles">
        <div
          v-for="(particle, index) in particleStyles"
          :key="index"
          class="particle"
          :style="particle"
        ></div>
      </div>
    </div>
  </div>
</template>


<script>
import { mapState, mapActions } from "vuex";

export default {
  name: 'ProgressBar',
  data: () => ({
    dialog: false,
    resolve: null,
    reject: null,
    progressValue: 0,
    currentStatus: 'Initializing...',
    progressInterval: null,
    requestProgress: {
      loaded: 0,
      total: 0,
      isRealProgress: false
    },
    activeRequests: new Set(),
    options: {
      color: "primary",
      width: 400,
      zIndex: 1000000000,
      title: 'Loading',
      subtitle: 'Please wait while we process your request...'
    },
    // Performance optimization data
    _lastProgressUpdate: 0,
    _lastStatusUpdate: '',
    _renderThrottle: null,
  }),
  computed: {
    ...mapState("app", ["progressBar"]),

    // Cached static data
    statusMessages() {
      return Object.freeze([
        'Initializing...',
        'Loading resources...',
        'Processing data...',
        'Finalizing...',
        'Almost done...'
      ]);
    },

    // Pre-computed particle styles (calculated once)
    particleStyles() {
      if (!this._particleStyles) {
        this._particleStyles = Object.freeze(
          Array.from({ length: 6 }, (_, index) => {
            const delay = index * 0.5;
            const duration = 3 + Math.random() * 2;
            return Object.freeze({
              animationDelay: `${delay}s`,
              animationDuration: `${duration}s`,
              left: `${10 + index * 15}%`,
              top: `${20 + Math.random() * 60}%`
            });
          })
        );
      }
      return this._particleStyles;
    },

    // Optimized computed properties
    overlayStyle() {
      return { zIndex: this.options.zIndex };
    },

    progressBarStyle() {
      return { width: `${this.progressValue}%` };
    },

    progressPercentage() {
      return Math.round(this.progressValue);
    },

    displayTitle() {
      return this.options.title || 'Loading';
    },

    displaySubtitle() {
      return this.options.subtitle || 'Please wait while we process your request...';
    },

    // Pre-computed dots array
    dots() {
      return Object.freeze(Array(4).fill(null));
    },

    // Computed active dots state
    activeDots() {
      return Object.freeze([
        this.progressValue > 20,
        this.progressValue > 40,
        this.progressValue > 60,
        this.progressValue > 80
      ]);
    },

    // Vue 2 compatible performance optimizations
    // Cache key for header to prevent unnecessary re-renders
    headerCacheKey() {
      return `${this.options.title}-${this.options.subtitle}`;
    },

    // Cache key for dots to prevent unnecessary re-renders
    dotsCacheKey() {
      return this.activeDots.join('-');
    },

    // Conditional rendering flags for performance
    shouldRenderHeader() {
      return this.dialog && (this.options.title || this.options.subtitle);
    },

    shouldRenderDots() {
      return this.dialog && this.progressValue >= 0;
    },

    // Optimized progress value with throttling
    throttledProgressValue() {
      // Round to nearest 5% to reduce unnecessary updates
      return Math.round(this.progressValue / 5) * 5;
    }
  },
  methods: {
    ...mapActions("app", ["loadVApp", "unLoadVApp"]),

    open(options) {
      this.loadVApp();
      this.dialog = true;
      this.options = Object.assign({}, this.options, options);
      this.resetProgress();
      this.startProgress();
      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      });
    },

    agree() {
      this.resolve?.(true);
      this.close();
    },

    cancel() {
      this.resolve?.(false);
      this.close();
    },

    close() {
      this.stopProgress();
      this.cleanup();
      this.dialog = false;
      this.unLoadVApp();
    },

    resetProgress() {
      this.progressValue = 0;
      this.currentStatus = this.statusMessages[0];
      this.requestProgress = {
        loaded: 0,
        total: 0,
        isRealProgress: false
      };
    },

    startProgress() {
      this.resetProgress();

      // Use requestAnimationFrame for smoother updates
      const updateProgress = () => {
        if (!this.dialog) return;

        let newProgressValue;

        if (this.requestProgress.isRealProgress) {
          // Use real progress from axios
          if (this.requestProgress.total > 0) {
            newProgressValue = Math.min(
              (this.requestProgress.loaded / this.requestProgress.total) * 100,
              95
            );
          } else {
            newProgressValue = this.progressValue;
          }
        } else if (this.progressValue < 95) {
          // Simulate realistic progress with easing
          const remaining = 95 - this.progressValue;
          const increment = remaining * 0.02 + Math.random() * 0.5;
          newProgressValue = Math.min(this.progressValue + increment, 95);
        } else {
          newProgressValue = this.progressValue;
        }

        // Use optimized progress update
        this.updateProgressOptimized(newProgressValue);

        // Continue animation
        if (this.progressValue < 100) {
          this.progressInterval = requestAnimationFrame(updateProgress);
        }
      };

      this.progressInterval = requestAnimationFrame(updateProgress);
    },

    updateStatusMessage() {
      const statusIndex = Math.floor((this.progressValue / 100) * this.statusMessages.length);
      const newStatus = this.statusMessages[Math.min(statusIndex, this.statusMessages.length - 1)];

      // Only update if status actually changed (Vue 2 compatible optimization)
      if (this.currentStatus !== newStatus && this._lastStatusUpdate !== newStatus) {
        this.currentStatus = newStatus;
        this._lastStatusUpdate = newStatus;
      }
    },

    // Vue 2 compatible throttled update method
    throttledUpdate(callback, delay = 16) {
      if (this._renderThrottle) {
        clearTimeout(this._renderThrottle);
      }

      this._renderThrottle = setTimeout(() => {
        callback();
        this._renderThrottle = null;
      }, delay);
    },

    // Optimized progress update with change detection
    updateProgressOptimized(newValue) {
      const now = Date.now();
      const roundedValue = Math.round(newValue);

      // Throttle updates to ~60fps and only update if value actually changed
      if (now - this._lastProgressUpdate > 16 && this.progressValue !== roundedValue) {
        this.progressValue = roundedValue;
        this._lastProgressUpdate = now;

        // Update status message efficiently
        this.throttledUpdate(() => {
          this.updateStatusMessage();
        }, 50); // Less frequent status updates
      }
    },

    stopProgress() {
      if (this.progressInterval) {
        cancelAnimationFrame(this.progressInterval);
        this.progressInterval = null;
      }
      this.progressValue = 100;
      this.currentStatus = 'Complete!';
    },

    // Method to update progress from axios interceptors
    updateRequestProgress(loaded, total) {
      this.requestProgress = {
        loaded,
        total,
        isRealProgress: true
      };
    },

    // Track active requests for multiple concurrent requests
    addActiveRequest(requestId) {
      this.activeRequests.add(requestId);
    },

    removeActiveRequest(requestId) {
      this.activeRequests.delete(requestId);
      if (this.activeRequests.size === 0) {
        // All requests completed
        setTimeout(() => {
          if (this.activeRequests.size === 0) {
            this.stopProgress();
          }
        }, 500);
      }
    },

    cleanup() {
      // Clear intervals and timeouts
      if (this.progressInterval) {
        cancelAnimationFrame(this.progressInterval);
        this.progressInterval = null;
      }

      // Clear throttle timeouts
      if (this._renderThrottle) {
        clearTimeout(this._renderThrottle);
        this._renderThrottle = null;
      }

      // Clear active requests
      this.activeRequests.clear();

      // Reset progress state
      this.requestProgress = {
        loaded: 0,
        total: 0,
        isRealProgress: false
      };

      // Reset performance tracking
      this._lastProgressUpdate = 0;
      this._lastStatusUpdate = '';

      // Clear resolve/reject functions
      this.resolve = null;
      this.reject = null;
    }
  },

  watch: {
    progressBar: {
      handler(newVal) {
        this.dialog = newVal;
        if (newVal) {
          this.loadVApp();
          this.startProgress();
        } else {
          this.stopProgress();
          this.cleanup();
          this.unLoadVApp();
        }
      },
      immediate: false
    },

    // Vue 2 compatible performance watchers
    'options.title': {
      handler() {
        // Force re-render of header when title changes
        this.$forceUpdate();
      }
    },

    'options.subtitle': {
      handler() {
        // Force re-render of header when subtitle changes
        this.$forceUpdate();
      }
    },

    // Optimized progress value watcher
    progressValue: {
      handler(newVal, oldVal) {
        // Only trigger updates if the rounded value actually changed
        const newRounded = Math.round(newVal);
        const oldRounded = Math.round(oldVal);

        if (newRounded !== oldRounded) {
          // Trigger minimal re-render for dots
          this.$nextTick(() => {
            // This ensures dots update efficiently
          });
        }
      }
    }
  },

  beforeUnmount() {
    this.cleanup();
  },

  // Expose methods for external access
  mounted() {
    // Make progress bar methods available globally for axios integration
    if (this.$root) {
      this.$root.$progressBar = {
        updateProgress: this.updateRequestProgress,
        addRequest: this.addActiveRequest,
        removeRequest: this.removeActiveRequest
      };
    }
  }
};
</script>

<style scoped>
/* Progress Overlay - GPU Accelerated */
.progress-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(8px);
  display: flex;
  justify-content: center;
  align-items: center;
  animation: fadeIn 0.3s ease-out;
  will-change: opacity;
  transform: translateZ(0); /* Force GPU layer */
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateZ(0) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateZ(0) scale(1);
  }
}

/* Progress Container */
.progress-container {
  position: relative;
  width: 100%;
  max-width: 450px;
  padding: 20px;
  transform: translateZ(0); /* Force GPU layer */
}

/* Animated Background - Optimized */
.progress-bg-animation {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(
    from 0deg,
    transparent,
    rgba(102, 126, 234, 0.1),
    transparent,
    rgba(118, 75, 162, 0.1),
    transparent
  );
  animation: rotate 8s linear infinite;
  border-radius: 50%;
  will-change: transform;
  transform: translateZ(0); /* Force GPU layer */
}

@keyframes rotate {
  from { transform: translateZ(0) rotate(0deg); }
  to { transform: translateZ(0) rotate(360deg); }
}

/* Main Progress Card - GPU Optimized */
.progress-card {
  position: relative;
  background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 20px;
  padding: 40px 30px;
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.1),
    0 8px 25px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: slideUp 0.5s ease-out;
  will-change: transform, opacity;
  transform: translateZ(0); /* Force GPU layer */
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateZ(0) translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateZ(0) translateY(0) scale(1);
  }
}

/* Progress Header */
.progress-header {
  text-align: center;
  margin-bottom: 30px;
}

.progress-icon {
  margin-bottom: 20px;
}

.icon-spinner {
  width: 60px;
  height: 60px;
  margin: 0 auto;
  animation: pulse 2s ease-in-out infinite;
  will-change: transform;
  transform: translateZ(0); /* Force GPU layer */
}

@keyframes pulse {
  0%, 100% { transform: translateZ(0) scale(1); }
  50% { transform: translateZ(0) scale(1.1); }
}

.spinner-svg {
  width: 100%;
  height: 100%;
  animation: spin 2s linear infinite;
  will-change: transform;
  transform: translateZ(0); /* Force GPU layer */
}

@keyframes spin {
  from { transform: translateZ(0) rotate(0deg); }
  to { transform: translateZ(0) rotate(360deg); }
}

.progress-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 8px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.progress-subtitle {
  font-size: 0.95rem;
  color: #6c757d;
  margin: 0;
  line-height: 1.4;
}

/* Progress Bar - Optimized */
.progress-bar-container {
  margin-bottom: 25px;
}

.progress-bar-track {
  width: 100%;
  height: 8px;
  background: linear-gradient(90deg, #e9ecef 0%, #f8f9fa 100%);
  border-radius: 10px;
  overflow: hidden;
  position: relative;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
  transform: translateZ(0); /* Force GPU layer */
}

.progress-bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 10px;
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1); /* Smoother easing */
  position: relative;
  overflow: hidden;
  will-change: width;
  transform: translateZ(0); /* Force GPU layer */
}

.progress-bar-glow {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: shimmer 2s infinite;
  will-change: transform;
  transform: translateZ(0); /* Force GPU layer */
}

@keyframes shimmer {
  0% { transform: translateZ(0) translateX(-100%); }
  100% { transform: translateZ(0) translateX(100%); }
}

.progress-percentage {
  text-align: center;
  margin-top: 10px;
  font-size: 1.1rem;
  font-weight: 600;
  color: #495057;
}

/* Animated Dots */
.progress-dots {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-bottom: 20px;
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #e9ecef;
  transition: all 0.3s ease;
  position: relative;
}

.dot.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 0 15px rgba(102, 126, 234, 0.5);
  animation: dotPulse 1s ease-in-out infinite;
  will-change: transform;
  transform: translateZ(0); /* Force GPU layer */
}

@keyframes dotPulse {
  0%, 100% { transform: translateZ(0) scale(1); }
  50% { transform: translateZ(0) scale(1.2); }
}

/* Status Message */
.progress-status {
  text-align: center;
}

.status-text {
  font-size: 0.9rem;
  color: #6c757d;
  font-weight: 500;
  animation: fadeInOut 2s ease-in-out infinite;
}

@keyframes fadeInOut {
  0%, 100% { opacity: 0.7; }
  50% { opacity: 1; }
}

/* Floating Particles */
.particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: hidden;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  animation: float 4s ease-in-out infinite;
  opacity: 0.6;
  will-change: transform, opacity;
  transform: translateZ(0); /* Force GPU layer */
}

@keyframes float {
  0%, 100% {
    transform: translateZ(0) translateY(0) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 0.6;
  }
  90% {
    opacity: 0.6;
  }
  50% {
    transform: translateZ(0) translateY(-30px) rotate(180deg);
    opacity: 1;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .progress-container {
    max-width: 350px;
    padding: 15px;
  }

  .progress-card {
    padding: 30px 20px;
    border-radius: 15px;
  }

  .progress-title {
    font-size: 1.3rem;
  }

  .progress-subtitle {
    font-size: 0.9rem;
  }

  .icon-spinner {
    width: 50px;
    height: 50px;
  }
}

@media (max-width: 480px) {
  .progress-container {
    max-width: 300px;
    padding: 10px;
  }

  .progress-card {
    padding: 25px 15px;
  }

  .progress-title {
    font-size: 1.2rem;
  }
}
</style>

<template>
  <!-- Modern Progress Bar Overlay -->
  <div v-if="dialog" class="progress-overlay" :style="{ zIndex: options.zIndex }">
    <div class="progress-container">
      <!-- Animated Background -->
      <div class="progress-bg-animation"></div>

      <!-- Main Progress Card -->
      <div class="progress-card">
        <!-- Header with Icon -->
        <div class="progress-header">
          <div class="progress-icon">
            <div class="icon-spinner">
              <svg viewBox="0 0 50 50" class="spinner-svg">
                <circle cx="25" cy="25" r="20" fill="none" stroke="url(#gradient)" stroke-width="3" stroke-linecap="round" stroke-dasharray="31.416" stroke-dashoffset="31.416">
                  <animate attributeName="stroke-array" dur="2s" values="0 31.416;15.708 15.708;0 31.416" repeatCount="indefinite"/>
                  <animate attributeName="stroke-dashoffset" dur="2s" values="0;-15.708;-31.416" repeatCount="indefinite"/>
                </circle>
                <defs>
                  <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
                  </linearGradient>
                </defs>
              </svg>
            </div>
          </div>
          <h4 class="progress-title">{{ options.title || 'Loading' }}</h4>
          <p class="progress-subtitle">{{ options.subtitle || 'Please wait while we process your request...' }}</p>
        </div>

        <!-- Progress Bar -->
        <div class="progress-bar-container">
          <div class="progress-bar-track">
            <div class="progress-bar-fill" :style="{ width: progressValue + '%' }">
              <div class="progress-bar-glow"></div>
            </div>
          </div>
          <div class="progress-percentage">{{ Math.round(progressValue) }}%</div>
        </div>

        <!-- Animated Dots -->
        <div class="progress-dots">
          <div class="dot" :class="{ active: progressValue > 20 }"></div>
          <div class="dot" :class="{ active: progressValue > 40 }"></div>
          <div class="dot" :class="{ active: progressValue > 60 }"></div>
          <div class="dot" :class="{ active: progressValue > 80 }"></div>
        </div>

        <!-- Status Message -->
        <div class="progress-status">
          <span class="status-text">{{ currentStatus }}</span>
        </div>
      </div>

      <!-- Floating Particles -->
      <div class="particles">
        <div v-for="i in 6" :key="i" class="particle" :style="getParticleStyle(i)"></div>
      </div>
    </div>
  </div>
</template>


<script>
import { mapState, mapActions } from "vuex";

export default {
  name: 'ProgressBar',
  data: () => ({
    dialog: false,
    resolve: null,
    reject: null,
    progressValue: 0,
    currentStatus: 'Initializing...',
    progressInterval: null,
    statusMessages: [
      'Initializing...',
      'Loading resources...',
      'Processing data...',
      'Finalizing...',
      'Almost done...'
    ],
    options: {
      color: "primary",
      width: 400,
      zIndex: 1000000000,
      title: 'Loading',
      subtitle: 'Please wait while we process your request...'
    },
  }),
  computed: {
    ...mapState("app", ["progressBar"])
  },
  methods: {
    ...mapActions("app", ["loadVApp", "unLoadVApp"]),

    open(options) {
      this.loadVApp();
      this.dialog = true;
      this.options = Object.assign(this.options, options);
      this.startProgress();
      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      });
    },

    agree() {
      this.resolve(true);
      this.close();
    },

    cancel() {
      this.resolve(false);
      this.close();
    },

    close() {
      this.stopProgress();
      this.dialog = false;
      this.unLoadVApp();
    },

    startProgress() {
      this.progressValue = 0;
      this.currentStatus = this.statusMessages[0];

      this.progressInterval = setInterval(() => {
        if (this.progressValue < 95) {
          // Simulate realistic progress with varying speeds
          const increment = Math.random() * 3 + 1;
          this.progressValue = Math.min(this.progressValue + increment, 95);

          // Update status message based on progress
          const statusIndex = Math.floor((this.progressValue / 100) * this.statusMessages.length);
          this.currentStatus = this.statusMessages[Math.min(statusIndex, this.statusMessages.length - 1)];
        }
      }, 200);
    },

    stopProgress() {
      if (this.progressInterval) {
        clearInterval(this.progressInterval);
        this.progressInterval = null;
      }
      this.progressValue = 100;
      this.currentStatus = 'Complete!';
    },

    getParticleStyle(index) {
      const delay = index * 0.5;
      const duration = 3 + Math.random() * 2;
      return {
        animationDelay: `${delay}s`,
        animationDuration: `${duration}s`,
        left: `${10 + index * 15}%`,
        top: `${20 + Math.random() * 60}%`
      };
    }
  },

  watch: {
    progressBar(newVal) {
      this.dialog = newVal;
      if (newVal) {
        this.loadVApp();
        this.startProgress();
      } else {
        this.stopProgress();
        this.unLoadVApp();
      }
    }
  },

  beforeUnmount() {
    this.stopProgress();
  }
};
</script>

<style scoped>
/* Progress Overlay */
.progress-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(8px);
  display: flex;
  justify-content: center;
  align-items: center;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Progress Container */
.progress-container {
  position: relative;
  width: 100%;
  max-width: 450px;
  padding: 20px;
}

/* Animated Background */
.progress-bg-animation {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(
    from 0deg,
    transparent,
    rgba(102, 126, 234, 0.1),
    transparent,
    rgba(118, 75, 162, 0.1),
    transparent
  );
  animation: rotate 8s linear infinite;
  border-radius: 50%;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Main Progress Card */
.progress-card {
  position: relative;
  background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 20px;
  padding: 40px 30px;
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.1),
    0 8px 25px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Progress Header */
.progress-header {
  text-align: center;
  margin-bottom: 30px;
}

.progress-icon {
  margin-bottom: 20px;
}

.icon-spinner {
  width: 60px;
  height: 60px;
  margin: 0 auto;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.spinner-svg {
  width: 100%;
  height: 100%;
  animation: spin 2s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.progress-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 8px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.progress-subtitle {
  font-size: 0.95rem;
  color: #6c757d;
  margin: 0;
  line-height: 1.4;
}

/* Progress Bar */
.progress-bar-container {
  margin-bottom: 25px;
}

.progress-bar-track {
  width: 100%;
  height: 8px;
  background: linear-gradient(90deg, #e9ecef 0%, #f8f9fa 100%);
  border-radius: 10px;
  overflow: hidden;
  position: relative;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progress-bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 10px;
  transition: width 0.3s ease;
  position: relative;
  overflow: hidden;
}

.progress-bar-glow {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

.progress-percentage {
  text-align: center;
  margin-top: 10px;
  font-size: 1.1rem;
  font-weight: 600;
  color: #495057;
}

/* Animated Dots */
.progress-dots {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-bottom: 20px;
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #e9ecef;
  transition: all 0.3s ease;
  position: relative;
}

.dot.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 0 15px rgba(102, 126, 234, 0.5);
  animation: dotPulse 1s ease-in-out infinite;
}

@keyframes dotPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.2); }
}

/* Status Message */
.progress-status {
  text-align: center;
}

.status-text {
  font-size: 0.9rem;
  color: #6c757d;
  font-weight: 500;
  animation: fadeInOut 2s ease-in-out infinite;
}

@keyframes fadeInOut {
  0%, 100% { opacity: 0.7; }
  50% { opacity: 1; }
}

/* Floating Particles */
.particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: hidden;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  animation: float 4s ease-in-out infinite;
  opacity: 0.6;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 0.6;
  }
  90% {
    opacity: 0.6;
  }
  50% {
    transform: translateY(-30px) rotate(180deg);
    opacity: 1;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .progress-container {
    max-width: 350px;
    padding: 15px;
  }

  .progress-card {
    padding: 30px 20px;
    border-radius: 15px;
  }

  .progress-title {
    font-size: 1.3rem;
  }

  .progress-subtitle {
    font-size: 0.9rem;
  }

  .icon-spinner {
    width: 50px;
    height: 50px;
  }
}

@media (max-width: 480px) {
  .progress-container {
    max-width: 300px;
    padding: 10px;
  }

  .progress-card {
    padding: 25px 15px;
  }

  .progress-title {
    font-size: 1.2rem;
  }
}
</style>

<template>
  <c-card class="filter-card">
    <c-card-header class="filter-header">
      <div class="header-content">
        <div class="header-icon">
          <i class="fas fa-filter fa-lg"></i>
        </div>
        <div class="header-title">
          <h4 class="mb-0">
            <i class="fas fa-chart-line text-white"></i>
            ROI Trend Report Filters
          </h4>
          <small class="text-white-50">Configure your report parameters</small>
        </div>
      </div>
    </c-card-header>
    <c-card-body class="filter-body">
      <c-tabs class="enhanced-tabs">
        <c-tab active>
          <template slot="title">
            <div class="tab-title">
              <i class="fas fa-chart-pie text-primary"></i>
              <span class="ml-2">Main Filters</span>
            </div>
          </template>
          <c-card class="tab-card">
            <c-card-body class="tab-body">
              <div class="row">
                <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-form-group class="enhanced-form-group">
                    <template #label>
                      <div class="form-label">
                        <i class="fas fa-sitemap text-primary"></i>
                        <span class="ml-2">Line <span class="required">*</span></span>
                      </div>
                    </template>
                    <template #input>
                      <div class="checkbox-wrapper" v-if="lines.length != 0">
                        <input id="line" class="enhanced-checkbox" type="checkbox"
                          v-model="checkAllLines" title="Check All lines" @change="checkAllLine" />
                        <label for="line" class="checkbox-label">
                          <i class="fas fa-check-double text-success"></i>
                          <span class="ml-2">Select All Lines</span>
                        </label>
                      </div>
                      <v-select
                        v-model="line_id"
                        :options="lines"
                        label="name"
                        :value="0"
                        :reduce="(line) => line.id"
                        placeholder="🏢 Select Line(s)"
                        class="enhanced-select mt-2"
                        @input="getLineData"
                        multiple />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-form-group>
                    <template #label> Approval Types </template>
                    <template #input>
                      <v-select v-model="approval_id" :options="approvals" label="name" :value="0"
                        :reduce="(approval) => approval.value" placeholder="Select Approval Types" class="mt-2" />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-form-group>
                    <template #label> Payments </template>
                    <template #input>
                      <input label="All" id="payment" v-if="payments.length != 0" class="m-1" type="checkbox"
                        v-model="checkAllPayments" title="Check All Payments" @change="checkAllPayment" />
                      <label for="payment" v-if="payments.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="payment_id" :options="payments" label="name" :value="0"
                        :reduce="(payment) => payment.id" placeholder="Select Payment Type" class="mt-2" multiple />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-input label="From" type="date" placeholder="From" v-model="from_date"></c-input>
                </div>

                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-input label="To" type="date" placeholder="To" v-model="to_date"></c-input>
                </div>
              </div>
            </c-card-body>
          </c-card>
        </c-tab>
        <c-tab>
          <template slot="title">
            <c-icon class="custom_icon" name="cil-chart-pie" /> Types & Products
          </template>
          <c-card>
            <c-card-body>
              <div class="row">
                <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-form-group>
                    <template #label> Products </template>
                    <template #input>
                      <input label="All" v-if="products.length != 0" id="product" class="m-1" type="checkbox"
                        v-model="checkAllProducts" title="Check All Products" @change="checkAllProduct" />
                      <label for="product" v-if="products.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="product_id" :options="products" label="name" :value="0"
                        :reduce="(product) => product.id" placeholder="Select Product" class="mt-2" multiple />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-form-group>
                    <template #label> Request Type </template>
                    <template #input>
                      <input label="All" id="type" v-if="types.length != 0" class="m-1" type="checkbox"
                        v-model="allRequestTypes" title="Check All Types" @change="checkAllRequestType" />
                      <label for="type" v-if="types.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="type_id" :options="types" label="name" :value="0" :reduce="(type) => type.id"
                        placeholder="Select Commercial Type" class="mt-2" multiple />
                    </template>
                  </c-form-group>
                </div>
              </div>
            </c-card-body>
          </c-card>
        </c-tab>
      </c-tabs>
    </c-card-body>
    <c-card-footer class="filter-footer">
      <div class="footer-content">
        <div class="filter-info">
          <i class="fas fa-info-circle text-info"></i>
          <span class="ml-2 text-muted">Select at least one line to generate the report</span>
        </div>
        <button
          v-if="line_id != null"
          @click="show"
          class="enhanced-show-btn"
          :disabled="!line_id || line_id.length === 0">
          <i class="fas fa-chart-line"></i>
          <span class="ml-2">Generate ROI Trend Report</span>
          <i class="fas fa-arrow-right ml-2"></i>
        </button>
      </div>
    </c-card-footer>
  </c-card>
</template>
<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import moment from "moment";

export default {
  components: {
    vSelect,
  },
  emits: ["getSchedule"],
  data() {
    return {
      product_id: [],
      type_id: [],
      line_id: [],
      divisions: [],
      approvals: [
        { name: "Pending", value: 1 },
        { name: "Approved", value: 2 },
        { name: "Disapproved", value: 3 },
        { name: "Pending & Approved", value: 4 },
        { name: "Total", value: 5 },
      ],
      approval_id: 5,
      payments: [],
      payment_id: [],
      products: [],
      types: [],
      lines: [],
      allRequestTypes: true,
      checkAllProducts: false,
      checkAllLines: false,
      checkAllPayments: false,
      from_date: moment().startOf('month').format("YYYY-MM-DD"),
      to_date: moment().endOf('month').format("YYYY-MM-DD"),
    };
  },
  methods: {
    initialize() {
      axios
        .get("/api/commercial-report-lines")
        .then((response) => {
          this.lines = response.data.data.lines;
          this.payments = response.data.data.payments;
          this.types = response.data.data.requestTypes;
          this.type_id = this.types.map((item) => item.id);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getLineData() {
      axios
        .post(`/api/get-line-commercial-cost`, {
          lines: this.line_id
        })
        .then((response) => {
          this.products = response.data.data;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    checkAllLine() {
      if (this.checkAllLines) this.line_id = this.lines.map((item) => item.id);
      if (this.checkAllLines == false) {
        this.line_id = null;
        this.product_id = null;
      }
      this.getLineData();
    },
    checkAllPayment() {
      if (this.checkAllPayments)
        this.payment_id = this.payments.map((item) => item.id);
      if (this.checkAllPayments == false) this.payment_id = null;
    },
    checkAllRequestType() {
      if (this.allRequestTypes)
        this.type_id = this.types.map((item) => item.id);
      if (this.allRequestTypes == false) this.type_id = null;
    },
    checkAllProduct() {
      if (this.checkAllProducts)
        this.product_id = this.products.map((item) => item.id);
      if (this.checkAllProducts == false) this.product_id = null;
    },
    checkAllDivs() {
      if (this.checkAllDivisions)
        this.division_id = this.divisions.map((item) => item.id);
      if (this.checkAllDivisions == false) this.division_id = null;
    },
    checkAllEmployees() {
      if (this.checkAllUsers) this.user_id = this.users.map((item) => item.id);
      if (this.checkAllUsers == false) this.user_id = null;
    },
    show() {
      let roiFilter = {
        lines: this.line_id,
        approval: this.approval_id,
        payments: this.payment_id,
        types: this.type_id,
        products: this.product_id,
        fromDate: this.from_date,
        toDate: this.to_date,
      };
      this.$emit("getSchedule", { roiFilter });
    },
  },
  created() {
    this.initialize();
  },
};
</script>
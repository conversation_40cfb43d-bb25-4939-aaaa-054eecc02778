<template>
  <c-col col="12" lg="12">
    <c-card>
      <c-card-header> {{ textHeader }}</c-card-header>
      <c-card-body>
        <c-tabs>
          <c-tab active>
            <template slot="title">
              <c-icon class="custom_icon" name="cil-chart-pie"/>
              Main Data
            </template>
            <c-card>
              <c-card-body>
                <div class="form-row form-group">
                  <div :class="getClasses">
                    <div class="form-group">
                      <label>
                        <strong>Action</strong>
                      </label>
                      <v-select
                        v-model="listFilter.status"
                        :options="statusbar"
                        label="name" required
                        :reduce="(status) => status.id"
                        placeholder="Select Action"
                        @input="getData"
                      />
                    </div>
                  </div>
                  <div :class="getClasses">
                    <div class="form-group">
                      <label>
                        <strong>Line</strong>
                      </label>
                      <v-select
                        v-model="listFilter.line_id"
                        :options="lines"
                        label="name"
                        required
                        :reduce="(line) => line.id"
                        placeholder="Select Line"
                      />

                    </div>
                  </div>
                  <div :class="getClasses">
                    <div class="form-group">
                      <label>
                        <strong>Division</strong>
                      </label>
                      <select-all-checkbox
                        :items="divisions"
                        v-model="checkAllDivisions"
                      />
                      <v-select
                        v-model="listFilter.div_id"
                        :options="divisions"
                        label="name"
                        :reduce="(division) => division.id"
                        placeholder="Select Division"
                        multiple
                      />

                    </div>
                  </div>
                  <div :class="getClasses">
                    <div class="form-group">
                      <label>
                        <strong>Brick</strong>
                      </label>
                      <select-all-checkbox
                        :items="bricks"
                        v-model="checkAllBricks"
                      />
                      <v-select
                        v-model="listFilter.brick_id"
                        :options="bricks"
                        label="name"
                        required
                        :reduce="(brick) => brick.id"
                        placeholder="Select Brick"
                        multiple
                      />
                    </div>
                  </div>
                </div>
              </c-card-body>
            </c-card>
          </c-tab>
          <c-tab>
            <template slot="title">
              <c-icon class="custom_icon" name="cil-description"/>
              Specialities
              & Classes
            </template>
            <c-card>
              <c-card-body>
                <div class="form-row form-group">
                  <div :class="getClasses">
                    <div class="form-group">
                      <label>
                        <strong>Specialities</strong>
                      </label>
                      <select-all-checkbox
                        :items="specialities"
                        v-model="checkAllSpecialities"
                      />
                      <v-select
                        v-model="listFilter.speciality_id"
                        :options="specialities"
                        label="name"
                        :reduce="(speciality) => speciality.id"
                        placeholder="Select Speciality"
                        multiple
                      />

                    </div>
                  </div>
                  <div :class="getClasses">
                    <div class="form-group">
                      <label>
                        <strong>Account Type</strong>
                      </label>
                      <select-all-checkbox
                        :items="types"
                        v-model="allAccountTypes"
                      />

                      <v-select
                        v-model="listFilter.type_id"
                        :options="types"
                        label="name"
                        :reduce="(type) => type.id"
                        placeholder="Select Account Type"
                        multiple
                      />

                    </div>
                  </div>
                  <div :class="getClasses">
                    <div class="form-group">
                      <label>
                        <strong>Classes</strong>
                      </label>
                      <select-all-checkbox
                        :items="classes"
                        v-model="checkAllClasses"
                      />
                      <v-select
                        v-model="listFilter.class_id"
                        :options="classes"
                        label="name"
                        required
                        :reduce="(clas) => clas.id"
                        placeholder="Select Class"
                        multiple/>
                    </div>
                  </div>
                </div>
              </c-card-body>
            </c-card>
          </c-tab>
        </c-tabs>
      </c-card-body>
      <c-card-footer>
        <c-button
          color="primary"
          class="text-white"
          @click="show()"
          :disabled="!(listFilter.line_id && listFilter.div_id)"
          style="float: right"
        >Show
        </c-button>
      </c-card-footer>
    </c-card>
  </c-col>
</template>
<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import SelectAllCheckbox from "../common/SelectAllCheckBox.vue";

export default {
  components: {
    SelectAllCheckbox,
    vSelect,
  },
  data() {
    return {
      listFilter: {
        line_id: null,
        div_id: null,
        brick_id: null,
        speciality_id: null,
        class_id: null,
        type_id: null,
        status: null,
        setting: null,
        month: null,
      },
      lines: [],
      statusbar: [
        {id: 1, name: "Activation"},
        {id: 2, name: "Inactivation"},
      ],
      divisions: [],
      bricks: [],
      specialities: [],
      months: [],
      classes: [],
      types: [],
      checkAllLines: false,
      checkAllDivisions: false,
      checkAllBricks: false,
      allAccountTypes: false,
      checkAllSpecialities: false,
      checkAllClasses: false,
    };
  },
  computed: {
    getClasses() {
      return 'col-lg-3 col-md-6 col-sm-8'
    },
    filteredLines() {
      return this.listFilter.line_id;
    },
    filteredDivisions() {
      return this.listFilter.div_id;
    }
  },
  props: {
    textHeader: {
      type: String,
      default: 'Create Favourite List'
    }
  },
  emits: ["getList"],
  methods: {
    initialize() {
      axios
        .post("/api/get-lines-with-dates", {
          from: null,
          to: null,
          data: ['lines', 'months', 'accountTypes', 'current_month', 'listSetting']
        })
        .then((response) => {
          this.lines = response.data.data.lines;
          this.listFilter.line_id = this.lines[0].id;
          this.types = response.data.data.accountTypes;
          this.listFilter.type_id = this.types.map((item) => item.id);
          this.months = response.data.data.months;
          this.listFilter.month = response.data.data.current_month;
          this.listFilter.setting = response.data.data.listSetting;
          this.getLineDivisions();
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getData() {
      this.initialize();
    },
    async getLineDivisions() {
      const res = await axios
        .post(`/api/get-lines-data-with-dates`, {
          lines: [this.listFilter.line_id],
          from: null,
          to: null,
          action: this.listFilter.status,
          data: ['divisions', 'classes', 'specialities']
        });

      this.divisions = res.data.data.divisions;
      this.classes = res.data.data.classes;
      this.specialities = res.data.data.specialities;
      this.selectedByDefaults();
    },
    selectedByDefaults() {
      this.checkAllDivisions = true;
      this.checkAllSpecialities = true;
      this.checkAllClasses = true;
      this.allAccountTypes = true;
    },

    async getDivisionBricks() {
      try {
        const res = await axios
          .post(`/api/favourite-list-bricks`, {
            divisions: this.listFilter.div_id,
          });

        this.bricks = res.data.data;
        this.checkAllBricks = true;
      } catch (error) {
        this.showErrorMessage(error);
      }

    },
    show() {
      this.$emit("getList", this.listFilter);
    },
  },
  watch: {
    filteredLines: {
      handler() {
        this.getLineDivisions()
      }
    },
    filteredDivisions: {
      handler() {
        this.getDivisionBricks()
      },
      deep: true
    },
    checkAllDivisions(value) {
      if (value) this.listFilter.div_id = this.divisions.map((item) => item.id);
      if (!value) this.listFilter.div_id = [];
    },
    checkAllBricks(value) {
      if (value) this.listFilter.brick_id = this.bricks.map((item) => item.id);
      if (!value) this.listFilter.brick_id = [];
    },
    checkAllSpecialities(value) {
      if (value)
        this.listFilter.speciality_id = this.specialities.map((item) => item.id);
      if (!value) this.listFilter.speciality_id = [];
    },
    checkAllClasses(value) {
      if (value)
        this.listFilter.class_id = this.classes.map((item) => item.id);
      if (!value) this.listFilter.class_id = [];
    },
    allAccountTypes(value) {
      if (value)
        this.listFilter.type_id = this.types.map((item) => item.id);
      if (!value) this.listFilter.type_id = [];
    }
  }
};
</script>

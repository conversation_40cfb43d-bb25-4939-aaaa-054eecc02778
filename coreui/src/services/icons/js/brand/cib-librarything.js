export const cibLibrarything = ["32 32","<path d='M13.865 6.375v-0.573l-0.036-0.615c0-0.172-0.068-0.443-0.188-0.802-0.12-0.344-0.172-0.651-0.172-0.917-0.005-0.177 0.083-0.344 0.224-0.443l0.469-0.375c0.234-0.172 0.521-0.255 0.813-0.24 0.214 0 0.547 0.026 1.026 0.094 0.411 0 0.719-0.094 0.932-0.266l0.495-0.344c0.224-0.188 0.344-0.391 0.344-0.615 0-0.385-0.208-0.693-0.635-0.932-0.505-0.25-1.068-0.37-1.63-0.349l-1.813 0.135c-0.411 0-1.198 0.094-2.359 0.276-1.161 0.203-2.052 0.297-2.708 0.297-0.266 0-0.719-0.057-1.359-0.161-0.505-0.089-1.016-0.135-1.531-0.146l-1 0.318c-0.349 0.109-0.505 0.266-0.505 0.469l0.078 0.401c0.188 0.37 0.453 0.667 0.797 0.865l0.365 0.188c0.172 0.052 0.318 0.078 0.438 0.078 0.146 0 0.375 0 0.667-0.036 0.234-0.005 0.464 0.057 0.667 0.172l0.495 0.438c0.24 0.271 0.401 0.536 0.505 0.776 0.109 0.24 0.161 0.464 0.161 0.667 0 0.161-0.026 0.401-0.094 0.76-0.052 0.344-0.078 0.625-0.078 0.823 0 0.271 0.063 0.656 0.198 1.151 0.135 0.49 0.198 0.906 0.198 1.224-0.036 0.505-0.052 1-0.052 1.495v1.495c0 0.385 0.042 0.88 0.094 1.49 0.052 0.615 0.094 1.109 0.094 1.469l-0.094 1.828-0.042 2.943-0.13 2.417c0 0.333 0.052 0.745 0.172 1.276 0.12 0.536 0.172 0.948 0.172 1.271 0 0.333-0.036 0.729-0.13 1.24-0.141 0.573-0.172 1.172-0.094 1.76 0.036 0.224 0.036 0.396 0 0.531-0.036 0.182-0.12 0.354-0.24 0.495l-0.417 0.505c-0.266 0.323-0.573 0.573-0.891 0.745-0.323 0.177-0.786 0.271-1.375 0.307-0.599 0.026-1.026 0.146-1.292 0.375-0.266 0.214-0.401 0.385-0.401 0.505 0 0.76 0.708 1.135 2.135 1.135l0.302-0.042 2.563-0.266c0.427-0.026 0.932-0.042 1.495-0.042h0.531l1.599 0.042h4.271l1.172-0.104c0.37-0.042 0.703-0.068 1-0.068 0.385 0 0.917 0.094 1.599 0.292 0.682 0.188 1.255 0.255 1.734 0.188l0.505-0.135c0.323-0.078 0.615-0.13 0.922-0.13l0.599 0.078c0.359 0.042 0.667 0.068 0.906 0.068 0.12 0 0.318-0.042 0.625-0.094 0.734-0.146 1.255-0.427 1.547-0.839 0.214-0.297 0.323-0.563 0.323-0.828-0.125-0.594-0.167-1.198-0.135-1.802 0.135-0.292 0.307-0.583 0.479-0.88 0.146-0.385 0.266-0.906 0.323-1.557 0.052-0.667 0.104-1.109 0.146-1.323 0.052-0.214 0.172-0.557 0.385-1.010 0.203-0.453 0.307-0.839 0.307-1.161 0-0.344-0.078-0.667-0.229-0.932-0.13-0.255-0.333-0.385-0.573-0.385-0.146 0-0.385 0.13-0.703 0.396-0.146 0.125-0.266 0.333-0.391 0.667-0.089 0.307-0.182 0.536-0.24 0.656-0.359 0.531-0.703 1.042-1.063 1.531-0.167 0.266-0.313 0.542-0.443 0.828-0.307 0.625-0.531 1.042-0.719 1.281-0.219 0.24-0.453 0.464-0.708 0.667-0.146 0.104-0.292 0.266-0.427 0.453-0.13 0.188-0.318 0.495-0.547 0.906l-0.76 0.401-0.531 0.13c-0.172 0.016-0.411 0.094-0.708 0.214-0.307 0.109-0.531 0.188-0.667 0.214-0.146 0.026-0.344 0.042-0.583 0.042-0.271 0-0.667-0.042-1.203-0.135-0.531-0.078-0.958-0.13-1.276-0.13l-0.938 0.052c-1.063 0-1.865-0.573-2.359-1.719-0.74-1.99-0.958-4.13-0.641-6.229 0.068-0.479 0.109-0.854 0.109-1.146v-3.068l0.036-1.010c0.042-0.417 0.057-0.708 0.057-0.88 0.005-0.484-0.068-0.958-0.229-1.417-0.099-0.234-0.156-0.49-0.172-0.745 0-0.135 0.026-0.349 0.078-0.615 0.068-0.266 0.094-0.464 0.094-0.615l-0.036-1.318c-0.042-0.557-0.057-1-0.057-1.318 0-0.375 0.068-0.844 0.188-1.401 0.12-0.563 0.172-1.016 0.172-1.359z'></path>"]
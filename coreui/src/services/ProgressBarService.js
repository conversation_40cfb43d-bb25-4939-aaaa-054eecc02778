/**
 * Enhanced Progress Bar Service
 * Provides centralized progress tracking and management
 */

class ProgressBarService {
  constructor() {
    this.activeRequests = new Map();
    this.progressCallbacks = new Set();
    this.errorCallbacks = new Set();
    this.completionCallbacks = new Set();
    this.isInitialized = false;
  }

  /**
   * Initialize the service with Vue instance
   */
  init(vueInstance) {
    this.vue = vueInstance;
    this.isInitialized = true;
    
    // Set up event listeners for axios events
    this.setupEventListeners();
  }

  /**
   * Set up event listeners for axios progress events
   */
  setupEventListeners() {
    if (typeof window === 'undefined') return;

    // Upload progress
    window.addEventListener('axios-upload-progress', (event) => {
      this.handleProgressUpdate(event.detail);
    });

    // Download progress
    window.addEventListener('axios-download-progress', (event) => {
      this.handleProgressUpdate(event.detail);
    });

    // Request errors
    window.addEventListener('axios-request-error', (event) => {
      this.handleRequestError(event.detail);
    });
  }

  /**
   * Handle progress updates from axios
   */
  handleProgressUpdate(detail) {
    const { requestId, loaded, total, progress } = detail;
    
    // Update request tracking
    if (this.activeRequests.has(requestId)) {
      this.activeRequests.set(requestId, {
        ...this.activeRequests.get(requestId),
        loaded,
        total,
        progress,
        lastUpdate: Date.now()
      });
    }

    // Calculate overall progress for multiple requests
    const overallProgress = this.calculateOverallProgress();
    
    // Notify callbacks
    this.progressCallbacks.forEach(callback => {
      try {
        callback({
          requestId,
          loaded,
          total,
          progress,
          overallProgress
        });
      } catch (error) {
        console.error('Progress callback error:', error);
      }
    });

    // Update Vue progress bar if available
    if (this.vue && this.vue.$root && this.vue.$root.$progressBar) {
      this.vue.$root.$progressBar.updateProgress(loaded, total);
    }
  }

  /**
   * Handle request errors
   */
  handleRequestError(detail) {
    const { requestId, error, message, status } = detail;
    
    // Remove from active requests
    this.activeRequests.delete(requestId);
    
    // Notify error callbacks
    this.errorCallbacks.forEach(callback => {
      try {
        callback({ requestId, error, message, status });
      } catch (callbackError) {
        console.error('Error callback error:', callbackError);
      }
    });
  }

  /**
   * Calculate overall progress for multiple concurrent requests
   */
  calculateOverallProgress() {
    if (this.activeRequests.size === 0) return 100;

    let totalLoaded = 0;
    let totalSize = 0;
    let requestsWithProgress = 0;

    this.activeRequests.forEach((request) => {
      if (request.total && request.total > 0) {
        totalLoaded += request.loaded || 0;
        totalSize += request.total;
        requestsWithProgress++;
      }
    });

    if (requestsWithProgress === 0) {
      // No requests with progress info, return estimated progress
      return Math.min(50 + (this.activeRequests.size * 10), 90);
    }

    return totalSize > 0 ? Math.round((totalLoaded / totalSize) * 100) : 0;
  }

  /**
   * Add a request to tracking
   */
  addRequest(requestId, config = {}) {
    this.activeRequests.set(requestId, {
      id: requestId,
      startTime: Date.now(),
      config,
      loaded: 0,
      total: 0,
      progress: 0
    });
  }

  /**
   * Remove a request from tracking
   */
  removeRequest(requestId) {
    const wasActive = this.activeRequests.has(requestId);
    this.activeRequests.delete(requestId);
    
    // If all requests completed, notify completion callbacks
    if (wasActive && this.activeRequests.size === 0) {
      this.completionCallbacks.forEach(callback => {
        try {
          callback();
        } catch (error) {
          console.error('Completion callback error:', error);
        }
      });
    }
    
    return wasActive;
  }

  /**
   * Get active request count
   */
  getActiveRequestCount() {
    return this.activeRequests.size;
  }

  /**
   * Get all active request IDs
   */
  getActiveRequestIds() {
    return Array.from(this.activeRequests.keys());
  }

  /**
   * Check if any requests are active
   */
  hasActiveRequests() {
    return this.activeRequests.size > 0;
  }

  /**
   * Subscribe to progress updates
   */
  onProgress(callback) {
    this.progressCallbacks.add(callback);
    
    // Return unsubscribe function
    return () => {
      this.progressCallbacks.delete(callback);
    };
  }

  /**
   * Subscribe to error events
   */
  onError(callback) {
    this.errorCallbacks.add(callback);
    
    return () => {
      this.errorCallbacks.delete(callback);
    };
  }

  /**
   * Subscribe to completion events (when all requests finish)
   */
  onCompletion(callback) {
    this.completionCallbacks.add(callback);
    
    return () => {
      this.completionCallbacks.delete(callback);
    };
  }

  /**
   * Clear all active requests (useful for cleanup)
   */
  clearAllRequests() {
    this.activeRequests.clear();
  }

  /**
   * Get request statistics
   */
  getStats() {
    const now = Date.now();
    const requests = Array.from(this.activeRequests.values());
    
    return {
      activeCount: requests.length,
      averageDuration: requests.length > 0 
        ? requests.reduce((sum, req) => sum + (now - req.startTime), 0) / requests.length 
        : 0,
      totalProgress: this.calculateOverallProgress(),
      oldestRequest: requests.length > 0 
        ? Math.min(...requests.map(req => req.startTime)) 
        : null
    };
  }

  /**
   * Cleanup method
   */
  destroy() {
    this.clearAllRequests();
    this.progressCallbacks.clear();
    this.errorCallbacks.clear();
    this.completionCallbacks.clear();
    
    // Remove event listeners
    if (typeof window !== 'undefined') {
      window.removeEventListener('axios-upload-progress', this.handleProgressUpdate);
      window.removeEventListener('axios-download-progress', this.handleProgressUpdate);
      window.removeEventListener('axios-request-error', this.handleRequestError);
    }
    
    this.isInitialized = false;
  }
}

// Create singleton instance
const progressBarService = new ProgressBarService();

export default progressBarService;

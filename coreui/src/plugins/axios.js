import axios from 'axios'
import {storePromise} from '../store';

// axios.defaults.baseURL = import.meta.env.BASE_API_URL

// Request tracking for concurrent requests
let activeRequests = new Map();
let requestIdCounter = 0;

storePromise.then(store=>{
  axios.interceptors.request.use(
    (config) => {
      // Generate unique request ID
      const requestId = `req_${++requestIdCounter}_${Date.now()}`;
      config.requestId = requestId;

      // Track active request
      activeRequests.set(requestId, {
        startTime: Date.now(),
        config: config
      });

      // Show progress bar
      store.dispatch("app/loadProgressBar");

      // Add progress tracking for uploads
      if (config.data instanceof FormData || config.headers['content-type'] === 'multipart/form-data') {
        config.onUploadProgress = (progressEvent) => {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);

          // Update progress bar if available
          if (window.Vue && window.Vue.$root && window.Vue.$root.$progressBar) {
            window.Vue.$root.$progressBar.updateProgress(progressEvent.loaded, progressEvent.total);
          }

          // Also trigger custom event for components that need it
          if (typeof window !== 'undefined') {
            window.dispatchEvent(new CustomEvent('axios-upload-progress', {
              detail: {
                requestId,
                loaded: progressEvent.loaded,
                total: progressEvent.total,
                progress
              }
            }));
          }
        };
      }

      // Add progress tracking for downloads
      config.onDownloadProgress = (progressEvent) => {
        if (progressEvent.lengthComputable) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);

          // Update progress bar if available
          if (window.Vue && window.Vue.$root && window.Vue.$root.$progressBar) {
            window.Vue.$root.$progressBar.updateProgress(progressEvent.loaded, progressEvent.total);
          }

          // Trigger custom event
          if (typeof window !== 'undefined') {
            window.dispatchEvent(new CustomEvent('axios-download-progress', {
              detail: {
                requestId,
                loaded: progressEvent.loaded,
                total: progressEvent.total,
                progress
              }
            }));
          }
        }
      };

      return config;
    },
    (error) => {
      // Clean up on request error
      if (error.config && error.config.requestId) {
        activeRequests.delete(error.config.requestId);
      }

      // Hide progress bar if no active requests
      if (activeRequests.size === 0) {
        store.dispatch("app/unLoadProgressBar");
      }

      return Promise.reject(error);
    }
  );

  axios.interceptors.response.use(
    (response) => {
      // Remove completed request from tracking
      if (response.config && response.config.requestId) {
        activeRequests.delete(response.config.requestId);
      }

      // Hide progress bar if no active requests
      if (activeRequests.size === 0) {
        setTimeout(() => {
          if (activeRequests.size === 0) {
            store.dispatch("app/unLoadProgressBar");
          }
        }, 300); // Small delay for better UX
      }

      return response;
    },
    (error) => {
      // Remove failed request from tracking
      if (error.config && error.config.requestId) {
        activeRequests.delete(error.config.requestId);
      }

      // Hide progress bar if no active requests
      if (activeRequests.size === 0) {
        setTimeout(() => {
          if (activeRequests.size === 0) {
            store.dispatch("app/unLoadProgressBar");
          }
        }, 300);
      }

      // Trigger error event for error handling
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('axios-request-error', {
          detail: {
            error,
            requestId: error.config?.requestId,
            message: error.message,
            status: error.response?.status
          }
        }));
      }

      return Promise.reject(error);
    }
  );

  // Expose axios and request utilities globally
  window.axios = axios;
  window.axiosUtils = {
    getActiveRequests: () => Array.from(activeRequests.keys()),
    getActiveRequestCount: () => activeRequests.size,
    cancelAllRequests: () => {
      // Note: This would require storing cancel tokens, which can be added if needed
      console.warn('Cancel all requests functionality would require cancel token implementation');
    }
  };
})


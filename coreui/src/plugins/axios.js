import axios from 'axios'
import {storePromise} from '../store';

window.axios = axios


storePromise.then(store => {
  axios.interceptors.request.use(
    (config) => {
      store.dispatch("app/loadProgressBar")
      return config;
    },
    (error) => {
      store.dispatch("app/unLoadProgressBar")
      return Promise.reject(error);
    }
  );

  axios.interceptors.response.use(
    (response) => {
      store.dispatch("app/unLoadProgressBar")
      return response;
    },
    (error) => {
      store.dispatch("app/unLoadProgressBar")
      return Promise.reject(error);
    }
  );
})


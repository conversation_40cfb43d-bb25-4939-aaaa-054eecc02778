import Vue from "vue";
import Vuex from "vuex";

Vue.use(Vuex);

class StoreManager {
  constructor() {
    this.store = null;
    this.isInitializing = false;
    this.initPromise = null;
  }

  // Synchronous getter - returns store if available, null otherwise
  get() {
    return this.store;
  }

  // Check if store is ready
  isReady() {
    return this.store !== null;
  }

  // Async initialization - only needed once
  async initialize() {
    // Return existing store if already created
    if (this.store) {
      return this.store;
    }

    // If currently initializing, wait for the existing promise
    if (this.isInitializing && this.initPromise) {
      return await this.initPromise;
    }

    // Start initialization
    this.isInitializing = true;
    this.initPromise = this.createStore();

    try {
      this.store = await this.initPromise;
      return this.store;
    } finally {
      this.isInitializing = false;
      this.initPromise = null;
    }
  }

  // Async getter for backward compatibility
  async getInstance() {
    return await this.initialize();
  }

  async createStore() {
    const modules = await this.loadModules();

    return new Vuex.Store({
      modules,
      state: this.getInitialState(),
      mutations: this.getMutations(),
      actions: this.getActions()
    });
  }

  getInitialState() {
    return {
      sidebarShow: "responsive",
      sidebarMinimize: false,
      asideShow: false,
      darkMode: false
    };
  }

  getMutations() {
    return {
      toggleSidebarDesktop(state) {
        const sidebarOpened = [true, "responsive"].includes(state.sidebarShow);
        state.sidebarShow = sidebarOpened ? false : "responsive";
      },
      toggleSidebarMobile(state) {
        const sidebarClosed = [false, "responsive"].includes(state.sidebarShow);
        state.sidebarShow = sidebarClosed ? true : "responsive";
      },
      set(state, [variable, value]) {
        state[variable] = value;
      },
      toggle(state, variable) {
        state[variable] = !state[variable];
      }
    };
  }

  getActions() {
    return {
      initializeStore({ commit, dispatch }) {
        return new Promise((resolve, reject) => {
          if (
            localStorage.getItem("api_token") &&
            localStorage.getItem("AuthUser")
          ) {
            commit("app/setApiToken", localStorage.getItem("api_token"));
            commit(
              "authentication/setAuthUser",
              JSON.parse(localStorage.getItem("AuthUser"))
            );
            commit("authentication/setImpersonated", !!localStorage.getItem("old_api_token"));
            dispatch("authentication/loadPermissions");
            commit(
              "authentication/setAccountLock",
              localStorage.getItem("AccountLocked") === "true"
            );
            resolve(true);
          } else {
            resolve(false);
          }
        });
      }
    };
  }

  async loadModules() {
    const modules = {};
    const requireComponents = import.meta.glob('./**/index.js');

    for (const fileName in requireComponents) {
      const module = await requireComponents[fileName]();
      const parts = fileName.split('/');
      const componentName = parts[parts.length - 2].split('-')
        .map((part, index) => index === 0 ? part : part[0].toUpperCase() + part.slice(1))
        .join('');
      modules[componentName] = module.default || module;
    }
    return modules;
  }

  // Method to reset the singleton (useful for testing)
  reset() {
    this.store = null;
    this.isInitializing = false;
    this.initPromise = null;
  }
}

// Create singleton instance
const storeManager = new StoreManager();

// Async initialization - call this once in your app startup
export const initializeStore = () => storeManager.initialize();

// Sync getter - use this everywhere else once initialized
export const getStore = () => storeManager.get();

// Check if store is ready
export const isStoreReady = () => storeManager.isReady();

// For backward compatibility, export a promise that resolves to the store
export const storePromise = storeManager.initialize();

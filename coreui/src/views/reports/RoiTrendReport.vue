<template>
  <c-col col="12" lg="12">
    <filter-data @getSchedule="filter" />
    <c-card v-if="items.length != 0" class="roi-trend-card">
      <c-card-header class="roi-header">
        <div class="header-content">
          <div class="report-icon">
            <i class="fas fa-chart-line fa-2x text-white"></i>
          </div>
          <div class="report-title">
            <h3 class="text-center text-white mb-0">
              <i class="fas fa-trending-up"></i> ROI Trend Report
            </h3>
            <p class="text-center text-white-50 mb-0">
              <i class="far fa-calendar-alt"></i> {{ roiData.fromDate }} to {{ roiData.toDate }}
            </p>
          </div>
          <div class="report-stats">
            <div class="stat-badge">
              <i class="fas fa-list-ol"></i>
              <span>{{ items.length }} Records</span>
            </div>
          </div>
        </div>
      </c-card-header>
      <c-card-body class="roi-body">
        <!-- Enhanced Data Table with Custom Styling -->
        <div class="table-wrapper">
          <c-data-table
            ref="content"
            hover
            header
            tableFilter
            striped
            sorter
            footer
            :items="enhancedItems"
            :fields="enhancedFields"
            :items-per-page="1000"
            :active-page="1"
            :responsive="true"
            pagination
            thead-top
            id="print"
            class="roi-table">
            <template slot="thead-top">
              <td style="border-top: none" class="total-header">
                <i class="fas fa-calculator text-primary"></i>
                <strong class="ml-2">Total Records</strong>
              </td>
              <td style="border-top: none" class="text-right total-value">
                <span class="badge badge-primary badge-lg">{{ items.length }}</span>
              </td>
            </template>

            <!-- Custom cell rendering for trend arrows -->
            <template v-for="field in trendFields" :slot="field" slot-scope="{ item }">
              <div :key="field" class="trend-cell" :class="getTrendClass(item[field])">
                <span class="trend-arrow" v-html="getTrendArrowHtml(item[field])"></span>
              </div>
            </template>

            <!-- Custom cell rendering for sales values -->
            <template v-for="field in salesFields" :slot="field" slot-scope="{ item }">
              <div :key="field" class="sales-cell" :class="getSalesClass(field)">
                <i :class="getSalesIcon(field)"></i>
                <span class="sales-value">{{ formatCurrency(item[field]) }}</span>
              </div>
            </template>

            <!-- Custom cell rendering for account names -->
            <template slot="account_name" slot-scope="{ item }">
              <div class="account-cell">
                <i class="fas fa-building text-info"></i>
                <span class="account-name">{{ item.account_name }}</span>
              </div>
            </template>

            <!-- Custom cell rendering for employee names -->
            <template slot="employee" slot-scope="{ item }">
              <div class="employee-cell">
                <i class="fas fa-user text-success"></i>
                <span class="employee-name">{{ item.employee }}</span>
              </div>
            </template>

            <!-- Custom cell rendering for doctor names -->
            <template slot="doctor_name" slot-scope="{ item }">
              <div class="doctor-cell">
                <i class="fas fa-user-md text-primary"></i>
                <span class="doctor-name">{{ item.doctor_name }}</span>
              </div>
            </template>

            <!-- Custom cell rendering for product names -->
            <template slot="product_name" slot-scope="{ item }">
              <div class="product-cell">
                <i class="fas fa-pills text-warning"></i>
                <span class="product-name">{{ item.product_name }}</span>
              </div>
            </template>

            <!-- Custom cell rendering for brand names -->
            <template slot="brand_name" slot-scope="{ item }">
              <div class="brand-cell">
                <i class="fas fa-tag text-danger"></i>
                <span class="brand-name">{{ item.brand_name }}</span>
              </div>
            </template>

          </c-data-table>
        </div>

        <!-- Summary Statistics Cards -->
        <div class="summary-stats mt-4" v-if="items.length > 0">
          <div class="row">
            <div class="col-md-3">
              <div class="stat-card stat-primary">
                <div class="stat-icon">
                  <i class="fas fa-chart-bar"></i>
                </div>
                <div class="stat-content">
                  <h4>Total Records</h4>
                  <p class="stat-value">{{ items.length }}</p>
                </div>
              </div>
            </div>
            <div class="col-md-3">
              <div class="stat-card stat-success">
                <div class="stat-icon">
                  <i class="fas fa-arrow-up"></i>
                </div>
                <div class="stat-content">
                  <h4>Positive Trends</h4>
                  <p class="stat-value">{{ countPositiveTrends() }}%</p>
                </div>
              </div>
            </div>
            <div class="col-md-3">
              <div class="stat-card stat-info">
                <div class="stat-icon">
                  <i class="fas fa-building"></i>
                </div>
                <div class="stat-content">
                  <h4>Avg Brick Sales</h4>
                  <p class="stat-value">{{ calculateAverage('brick_sales_before') }}</p>
                </div>
              </div>
            </div>
            <div class="col-md-3">
              <div class="stat-card stat-warning">
                <div class="stat-icon">
                  <i class="fas fa-pills"></i>
                </div>
                <div class="stat-content">
                  <h4>Avg Pharm Sales</h4>
                  <p class="stat-value">{{ calculateAverage('linked_pharm_before') }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </c-card-body>
      <c-card-footer>
        <download @getPrint="print" @getxlsx="download" @getpdf="createPDF" @getcsv="downloadCsv" :fields="fields"
          :data="items" :name="name" />
      </c-card-footer>
    </c-card>
  </c-col>
</template>

<script>
import FilterData from "../../components/reports/RoiTrend/filterData.vue";
import download from "../../components/download-reports/download.vue";
export default {
  name: "RoiTrendReport",
  components: {
    FilterData,
    download,
  },
  data: () => {
    return {
      items: [],
      fields: [],
      dates:[],
      roiData: {},
      name: "ROI Trend Report",
      loading: false,
    };
  },
  computed: {
    enhancedItems() {
      return this.items;
    },
    enhancedFields() {
      return this.fields.map(field => {
        if (typeof field === 'string') {
          return {
            key: field,
            label: this.getFieldLabel(field),
            sortable: true,
            _classes: this.getFieldClass(field)
          };
        }
        return {
          ...field,
          label: this.getFieldLabel(field.key || field.label),
          _classes: this.getFieldClass(field.key || field.label)
        };
      });
    },
    trendFields() {
      return this.fields.filter(field => {
        const key = typeof field === 'string' ? field : field.key;
        return key && key.includes('arrow');
      }).map(field => typeof field === 'string' ? field : field.key);
    },
    salesFields() {
      return this.fields.filter(field => {
        const key = typeof field === 'string' ? field : field.key;
        return key && (key.includes('sales') || key.includes('pharm')) && !key.includes('arrow');
      }).map(field => typeof field === 'string' ? field : field.key);
    }
  },
  emits: ["downloaded"],
  methods: {
    filter({ roiFilter }) {
      this.roiData = roiFilter;
      axios
        .post(`/api/roi-trend-report`, {
          roiFilter,
        })
        .then((response) => {
          this.items = response.data.data;
          this.fields = response.data.fields;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    print() {
      this.$htmlToPaper("print");
    },
    download() {
      this.downloadStyledExcel(
        this.dates,
        this.items,
        this.fields,
        'ROI Trend Report',
        'ROI Trend Data:'
      );
    },
    downloadCsv() {
      this.downloadXlsx(this.items, "ROI Trend Report.csv");
      this.$emit("downloaded");
    },

    // Enhanced styling methods
    getFieldLabel(key) {
      const labels = {
        'id': 'ID',
        'created_at': 'Date',
        'account_name': 'Account',
        'division': 'Division',
        'brick': 'Brick',
        'line': 'Line',
        'employee': 'Employee',
        'doctor_name': 'Doctor',
        'product_name': 'Product',
        'brand_name': 'Brand',
        'brick_sales_before': 'Brick Sales (Before)',
        'linked_pharm_before': 'Linked Pharm (Before)',
        'brick_sales_factor': 'Brick Factor',
        'linked_pharm_factor': 'Pharm Factor',
        'total_brick_sales_after': 'Total Brick (After)',
        'total_linked_pharm_after': 'Total Pharm (After)',
        'brick_sales_trend': 'Brick Trend',
        'linked_pharm_trend': 'Pharm Trend'
      };

      // Handle dynamic month columns
      if (key.includes('brick_sales_month_')) {
        const month = key.replace('brick_sales_month_', '');
        return `Brick M${month}`;
      }
      if (key.includes('brick_sales_arrow_')) {
        const month = key.replace('brick_sales_arrow_', '');
        return `B${month} Trend`;
      }
      if (key.includes('linked_pharm_month_')) {
        const month = key.replace('linked_pharm_month_', '');
        return `Pharm M${month}`;
      }
      if (key.includes('linked_pharm_arrow_')) {
        const month = key.replace('linked_pharm_arrow_', '');
        return `P${month} Trend`;
      }

      return labels[key] || key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    },

    getFieldClass(key) {
      if (key.includes('arrow') || key.includes('trend')) {
        return 'trend-column';
      }
      if (key.includes('sales') || key.includes('pharm')) {
        return 'sales-column';
      }
      if (key.includes('before')) {
        return 'before-column';
      }
      if (key.includes('after') || key.includes('total')) {
        return 'after-column';
      }
      return 'default-column';
    },

    getTrendClass(value) {
      switch (value) {
        case '↑': return 'trend-up';
        case '↓': return 'trend-down';
        case '→': return 'trend-stable';
        default: return 'trend-neutral';
      }
    },

    getTrendArrowHtml(value) {
      const arrowMap = {
        '↑': '<i class="fas fa-arrow-up text-success"></i>',
        '↓': '<i class="fas fa-arrow-down text-danger"></i>',
        '→': '<i class="fas fa-arrow-right text-warning"></i>'
      };
      return arrowMap[value] || '<i class="fas fa-minus text-muted"></i>';
    },

    getSalesClass(field) {
      if (field.includes('brick')) {
        return 'brick-sales';
      }
      if (field.includes('pharm')) {
        return 'pharm-sales';
      }
      if (field.includes('before')) {
        return 'before-sales';
      }
      if (field.includes('after') || field.includes('total')) {
        return 'after-sales';
      }
      return 'default-sales';
    },

    getSalesIcon(field) {
      if (field.includes('brick')) {
        return 'fas fa-building text-info';
      }
      if (field.includes('pharm')) {
        return 'fas fa-pills text-warning';
      }
      if (field.includes('factor')) {
        return 'fas fa-calculator text-primary';
      }
      return 'fas fa-dollar-sign text-success';
    },

    formatCurrency(value) {
      if (!value || value === '0.00') return '0.00';
      return parseFloat(value.toString().replace(/,/g, '')).toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });
    },

    calculateAverage(field) {
      if (this.items.length === 0) return '0.00';

      const sum = this.items.reduce((acc, item) => {
        return acc + parseFloat(item[field]?.toString().replace(/,/g, '') || 0);
      }, 0);

      return (sum / this.items.length).toFixed(2);
    },

    countPositiveTrends() {
      if (this.items.length === 0) return 0;

      const positiveCount = this.items.filter(item =>
        item.brick_sales_trend === '↑' || item.linked_pharm_trend === '↑'
      ).length;

      return Math.round((positiveCount / this.items.length) * 100);
    },
  },
};
</script>

<style scoped>
/* ROI Trend Report Enhanced Styles */

.roi-trend-card {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-radius: 15px;
  overflow: hidden;
  border: none;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.roi-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  padding: 20px;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.report-icon {
  flex: 0 0 auto;
}

.report-title {
  flex: 1;
  text-align: center;
}

.report-title h3 {
  font-size: 1.8rem;
  font-weight: 700;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.report-stats {
  flex: 0 0 auto;
}

.stat-badge {
  background: rgba(255, 255, 255, 0.2);
  padding: 8px 15px;
  border-radius: 20px;
  color: white;
  font-weight: 600;
  backdrop-filter: blur(10px);
}

.stat-badge i {
  margin-right: 5px;
}

.roi-body {
  padding: 25px;
  background: white;
}

.table-wrapper {
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.roi-table {
  margin-bottom: 0;
}

/* Enhanced Table Headers */
.roi-table thead th {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
  font-weight: 600;
  text-align: center;
  padding: 15px 10px;
  border: none;
  font-size: 0.9rem;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

.total-header {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%) !important;
  color: white !important;
  font-weight: bold;
}

.total-value {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%) !important;
}

.badge-lg {
  font-size: 1rem;
  padding: 8px 15px;
}

/* Column-specific styling */
.trend-column {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  width: 60px;
  text-align: center;
}

.sales-column {
  background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%);
}

.before-column {
  background: linear-gradient(135deg, #89f7fe 0%, #66a6ff 100%);
}

.after-column {
  background: linear-gradient(135deg, #48c6ef 0%, #6f86d6 100%);
}

/* Enhanced Cell Styling */
.trend-cell {
  text-align: center;
  padding: 10px;
  border-radius: 8px;
  margin: 2px;
  font-size: 1.2rem;
  font-weight: bold;
  transition: all 0.3s ease;
}

.trend-up {
  background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
  color: #2d5016;
  box-shadow: 0 2px 8px rgba(86, 171, 47, 0.3);
}

.trend-down {
  background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(255, 65, 108, 0.3);
}

.trend-stable {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(240, 147, 251, 0.3);
}

.trend-neutral {
  background: linear-gradient(135deg, #e0e0e0 0%, #f0f0f0 100%);
  color: #666;
}

.trend-arrow {
  font-size: 1.4rem;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

/* Sales Cell Styling */
.sales-cell {
  display: flex;
  align-items: center;
  padding: 8px;
  border-radius: 6px;
  margin: 2px;
  transition: all 0.3s ease;
}

.sales-cell:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.brick-sales {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.pharm-sales {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.before-sales {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.after-sales {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  color: #2d5016;
}

.default-sales {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  color: #333;
}

.sales-value {
  margin-left: 8px;
  font-weight: 600;
  font-size: 0.95rem;
}

/* Entity Cell Styling */
.account-cell, .employee-cell, .doctor-cell, .product-cell, .brand-cell {
  display: flex;
  align-items: center;
  padding: 8px;
  border-radius: 6px;
  margin: 2px;
  transition: all 0.3s ease;
}

.account-cell {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.employee-cell {
  background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
  color: #2d5016;
}

.doctor-cell {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.product-cell {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.brand-cell {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  color: #333;
}

.account-name, .employee-name, .doctor-name, .product-name, .brand-name {
  margin-left: 8px;
  font-weight: 600;
  font-size: 0.9rem;
}

/* Summary Statistics */
.summary-stats {
  margin-top: 30px;
}

.stat-card {
  background: white;
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: none;
  overflow: hidden;
  position: relative;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.stat-primary::before {
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.stat-success::before {
  background: linear-gradient(90deg, #56ab2f 0%, #a8e6cf 100%);
}

.stat-info::before {
  background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
}

.stat-warning::before {
  background: linear-gradient(90deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon {
  font-size: 2.5rem;
  margin-bottom: 15px;
  text-align: center;
}

.stat-primary .stat-icon {
  color: #667eea;
}

.stat-success .stat-icon {
  color: #56ab2f;
}

.stat-info .stat-icon {
  color: #4facfe;
}

.stat-warning .stat-icon {
  color: #f093fb;
}

.stat-content h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #666;
  margin-bottom: 10px;
  text-align: center;
}

.stat-value {
  font-size: 2rem;
  font-weight: bold;
  text-align: center;
  margin: 0;
}

.stat-primary .stat-value {
  color: #667eea;
}

.stat-success .stat-value {
  color: #56ab2f;
}

.stat-info .stat-value {
  color: #4facfe;
}

.stat-warning .stat-value {
  color: #f093fb;
}

/* Table Row Hover Effects */
.roi-table tbody tr {
  transition: all 0.3s ease;
}

.roi-table tbody tr:hover {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  transform: scale(1.01);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    text-align: center;
  }

  .report-icon, .report-stats {
    margin: 10px 0;
  }

  .roi-table {
    font-size: 0.8rem;
  }

  .trend-cell, .sales-cell {
    padding: 5px;
    font-size: 0.8rem;
  }

  .stat-card {
    margin-bottom: 15px;
  }
}

/* Print Styles */
@media print {
  .roi-trend-card {
    box-shadow: none;
    background: white;
  }

  .roi-header {
    background: #667eea !important;
    -webkit-print-color-adjust: exact;
  }

  .trend-cell, .sales-cell {
    -webkit-print-color-adjust: exact;
  }
}
</style>

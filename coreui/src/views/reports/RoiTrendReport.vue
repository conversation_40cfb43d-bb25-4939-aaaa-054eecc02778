<template>
  <c-col col="12" lg="12">
    <filter-data @getSchedule="filter" />
    <c-card v-if="items.length != 0">
      <c-card-header>
        <h3 class="text-center">
          ROI Trend Report <br />
          from: {{ roiData.fromDate }} to: {{ roiData.toDate }}
        </h3>
      </c-card-header>
      <c-card-body>
        <c-data-table ref="content" hover header tableFilter striped sorter footer :items="items" :fields="fields"
          :items-per-page="1000" :active-page="1" :responsive="true" pagination thead-top id="print">
          <template slot="thead-top">
            <td style="border-top: none"><strong>Total</strong></td>
            <td style="border-top: none" class="text-xs-right">
              {{ items.length }}
            </td>
          </template>
        </c-data-table>
      </c-card-body>
      <c-card-footer>
        <download @getPrint="print" @getxlsx="download" @getpdf="createPDF" @getcsv="downloadCsv" :fields="fields"
          :data="items" :name="name" />
      </c-card-footer>
    </c-card>
  </c-col>
</template>

<script>
import FilterData from "../../components/reports/RoiTrend/filterData.vue";
import download from "../../components/download-reports/download.vue";
export default {
  name: "RoiTrendReport",
  components: {
    FilterData,
    download,
  },
  data: () => {
    return {
      items: [],
      fields: [],
      dates:[],
      roiData: {},
      name: "ROI Trend Report",
    };
  },
  emits: ["downloaded"],
  methods: {
    filter({ roiFilter }) {
      this.roiData = roiFilter;
      axios
        .post(`/api/roi-trend-report`, {
          roiFilter,
        })
        .then((response) => {
          this.items = response.data.data;
          this.fields = response.data.fields;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    print() {
      this.$htmlToPaper("print");
    },
    download() {
      this.downloadStyledExcel(
        this.dates,
        this.items,
        this.fields,
        'ROI Trend Report',
        'ROI Trend Data:'
      );
    },
    downloadCsv() {
      this.downloadXlsx(this.items, "ROI Trend Report.csv");
      this.$emit("downloaded");
    },
  },
};
</script>

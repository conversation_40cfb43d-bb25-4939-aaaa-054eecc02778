<template>
  <c-col col="12" lg="12">
    <filter-data @getSchedule="filter" />
    <c-card v-if="items.length != 0">
      <c-card-header>
        <h3 class="text-center">
          Commercial Costs Report for Line: {{ items[0].line }} <br />
          from: {{ commercialData.fromDate }} to: {{ commercialData.toDate }}
        </h3>
      </c-card-header>
      <c-card-body>
        <c-data-table ref="content" hover header tableFilter striped sorter footer :items="items" :fields="fields"
          :items-per-page="1000" :active-page="1" :responsive="true" pagination thead-top id="print">
          <template slot="thead-top">
            <td style="border-top: none"><strong>Total</strong></td>
            <td style="border-top: none" class="text-xs-right">
              {{ items.length }}
            </td>
          </template>
          <template v-for="field in clickable_fields" v-slot:[field]="{ item }">
            <td :key="field">
              <h6 @click="rowClickHandler(item, field)" v-bind:style="{ cursor: 'pointer' }">{{ item[field] }}</h6>
            </td>
          </template>
          <template #title="{ item }">
            <td @click="showMore(item.id)" v-if="!readMore[item.id]">
              {{ item.title.substring(0, 15) + ".." }} <br /><span style="color: blue; cursor: pointer">show</span>
            </td>
            <td @click="showLess(item.id)" v-if="readMore[item.id]">
              {{ item.title }} <br />
              <span style="color: red; cursor: pointer">hide</span>
            </td>
          </template>
          <template #division="{ item }">
            <td>
              <strong :style="{
                color: item.color
              }">{{ item.division }}</strong>
            </td>
          </template>
          <template #employee="{ item }">
            <td>
              {{ item.employee }}
            </td>
          </template>
        </c-data-table>
      </c-card-body>
      <c-card-footer>
        <download @getPrint="print" @getxlsx="download" @getpdf="createPDF" @getcsv="downloadCsv" :fields="fields"
          :data="items" :name="name" />
      </c-card-footer>
    </c-card>
  </c-col>
</template>

<script>
import download from "../../components/download-reports/download.vue";
import filterData from "../../components/reports/CommercialCosts/filterData.vue";
import { Amiri } from "../../assets/fonts/Amiri-Regular-normal";
import jsPDF from "jspdf";
import "jspdf-autotable";
import { capitalize } from "../../filters";

export default {
  components: {
    download,
    filterData,
    capitalize
  },
  data: () => {
    return {
      clickable_fields: [],
      readMore: {},
      items: [],
      fields: [],
      expenseData: {},
      lineName: null,
      name: "Commercial Cost Report"
    };
  },
  emits: ["downloaded"],
  methods: {
    showMore(id) {
      this.$set(this.readMore, id, true);
    },
    showLess(id) {
      this.$set(this.readMore, id, false);
    },
    rowClickHandler(item, field) {
      axios
        .post(`/api/show-data-commercial-costs`, {
          listFilter: this.commercialData,
          id: item.id,
          column: field,
        })
        .then((response) => {
          const data = response.data.data;
          this.$root.$table("Commercial Report", data);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    print() {
      this.$htmlToPaper("print");
    },
    download() {
      let filteredData = Object.values(this.items);
      filteredData.forEach(element => {
        delete element["color"];
      });

      this.downloadXlsx(filteredData, "Commercial Cost Report.xlsx");
      this.$emit("downloaded");
    },
    downloadCsv() {
      let filteredData = Object.values(this.items);
      filteredData.forEach(element => {
        delete element["color"];
      });

      this.downloadXlsx(filteredData, "Commercial Cost Report.csv");
      this.$emit("downloaded");
    },
    createPDF() {
      let pdfName = "Commercial Costss Report";
      const columns = this.fields.map((field) => {
        return {
          title: capitalize(field),
          dataKey: field,
        };
      });
      const body = this.items;
      const doc = new jsPDF({ filters: ["ASCIIHexEncode"] });

      doc.addFileToVFS("Amiri-Regular.ttf", Amiri);
      doc.addFont("Amiri-Regular.ttf", "Amiri", "normal");
      doc.setFont("Amiri");
      doc.setFontSize(10);
      doc.autoTable({
        columns,
        body,
        margin: { top: 10 },
        showHead: "firstPage",
        styles: {
          lineColor: "#c7c7c7",
          lineWidth: 0,
          cellPadding: 2,
          font: "Amiri"
        }
      });
      doc.save(pdfName + ".pdf");
    },
    filter({ commercialFilter }) {
      this.commercialData = commercialFilter;
      axios
        .post(`/api/commercial-costs`, {
          commercialFilter
        })
        .then(response => {
          this.items = response.data.data;
          this.fields = response.data.fields;
          this.clickable_fields = response.data.clickable_fields;
        })
        .catch(error => {
          this.showErrorMessage(error);
        });
    }
  }
};
</script>

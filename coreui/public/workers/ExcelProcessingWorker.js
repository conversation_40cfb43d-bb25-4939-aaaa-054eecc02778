/**
 * Web Worker for processing large Excel datasets
 * Handles data transformation and chunked processing to prevent main thread blocking
 */

// Try to import ExcelJS with fallback methods
let ExcelJS = null;

try {
    // Method 1: Try CDN import
    importScripts('https://unpkg.com/exceljs@4.3.0/dist/exceljs.min.js');
    ExcelJS = self.ExcelJS;
} catch (error) {
    console.error('Failed to load ExcelJS from CDN:', error);

    try {
        // Method 2: Try alternative CDN
        importScripts('https://cdn.jsdelivr.net/npm/exceljs@4.3.0/dist/exceljs.min.js');
        ExcelJS = self.ExcelJS;
    } catch (error2) {
        console.error('Failed to load ExcelJS from alternative CDN:', error2);

        // Method 3: Signal that ExcelJS is not available
        self.postMessage({
            type: 'ERROR',
            error: {
                message: 'ExcelJS library could not be loaded in Web Worker. Falling back to main thread processing.',
                code: 'EXCELJS_LOAD_FAILED'
            }
        });
    }
}

let workbook = null;
let worksheets = new Map(); // Store multiple worksheets
let processedRows = 0;
let sheetConfig = null;

self.addEventListener('message', async function(e) {
    const { type, data } = e.data;
    
    try {
        switch (type) {
            case 'INIT_WORKBOOK':
                await initializeWorkbook(data);
                break;
                
            case 'PROCESS_CHUNK':
                await processDataChunk(data);
                break;

            case 'CREATE_SHEET':
                await createSheet(data);
                break;

            case 'PROCESS_SHEET_CHUNK':
                await processSheetChunk(data);
                break;

            case 'FINALIZE_WORKBOOK':
                await finalizeWorkbook(data);
                break;

            case 'GENERATE_BUFFER':
                await generateBuffer();
                break;
                
            default:
                throw new Error(`Unknown message type: ${type}`);
        }
    } catch (error) {
        self.postMessage({
            type: 'ERROR',
            error: {
                message: error.message,
                stack: error.stack
            }
        });
    }
});

async function initializeWorkbook({ title, dates, headerItems, subTitle, sheetConfig }) {
    // Check if ExcelJS is available
    if (!ExcelJS) {
        throw new Error('ExcelJS library not available in Web Worker');
    }

    workbook = new ExcelJS.Workbook();
    worksheets.clear();
    processedRows = 0;

    // Store sheet configuration
    if (sheetConfig && sheetConfig.useMultiSheet) {
        // Multi-sheet mode - sheets will be created individually
        self.postMessage({
            type: 'WORKBOOK_INITIALIZED',
            processedRows: 0,
            multiSheet: true,
            totalSheets: sheetConfig.totalSheets
        });
        return;
    }

    // Single sheet mode - create the main worksheet
    const worksheet = workbook.addWorksheet(title);
    worksheets.set(0, worksheet);

    // Setup styles and headers for single sheet
    await setupWorksheetHeaders(worksheet, dates, headerItems, title, subTitle);

    self.postMessage({
        type: 'WORKBOOK_INITIALIZED',
        processedRows: 0,
        multiSheet: false
    });
}

async function setupWorksheetHeaders(worksheet, dates, headerItems, title, subTitle) {
    // Setup styles
    const headerStyle = {
        font: { bold: true, color: { argb: 'FFFFFF' }, size: 11 },
        fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: '002060' } },
        alignment: { horizontal: 'center', vertical: 'middle' },
    };

    const subHeaderStyle = {
        font: { bold: true, color: { argb: 'FFFFFF' }, size: 10 },
        fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: '4472C4' } },
        alignment: { horizontal: 'center', vertical: 'middle' },
    };

    // Title row
    const titleRow = worksheet.getRow(1);
    titleRow.height = 30;
    const titleCell = titleRow.getCell(1);
    titleCell.value = title;
    titleCell.style = headerStyle;
    worksheet.mergeCells(1, 1, 1, headerItems.length);

    // Subtitle row
    const subTitleRow = worksheet.getRow(2);
    subTitleRow.height = 25;
    const subTitleCell = subTitleRow.getCell(1);
    subTitleCell.value = subTitle;
    subTitleCell.style = subHeaderStyle;
    worksheet.mergeCells(2, 1, 2, headerItems.length);

    // Date headers (rows 4-14)
    if (dates && dates.length > 0) {
        dates.forEach((date, index) => {
            const row = worksheet.getRow(4 + index);
            row.height = 25;
            headerItems.forEach((header, colIndex) => {
                const cell = row.getCell(1 + colIndex);
                cell.value = date[header] || '';
                cell.style = subHeaderStyle;
            });
        });
    }

    // Main headers row (row 15)
    const headerRow = worksheet.getRow(15);
    headerRow.height = 30;
    headerItems.forEach((header, index) => {
        const cell = headerRow.getCell(1 + index);
        cell.value = header;
        cell.style = headerStyle;
    });
}

async function createSheet({ sheetName, sheetIndex }) {
    const worksheet = workbook.addWorksheet(sheetName);
    worksheets.set(sheetIndex, worksheet);

    self.postMessage({
        type: 'SHEET_CREATED',
        sheetIndex: sheetIndex,
        sheetName: sheetName
    });
}

async function processSheetChunk({ chunk, headerItems, title, startRowIndex, sheetIndex, sheetName }) {
    const worksheet = worksheets.get(sheetIndex);
    if (!worksheet) {
        throw new Error(`Worksheet not found for sheet index: ${sheetIndex}`);
    }

    // Setup headers if this is the first chunk for this sheet
    if (startRowIndex === 16) {
        await setupWorksheetHeaders(worksheet, [], headerItems, title, `${title} - ${sheetName}`);
    }

    await processDataChunkForWorksheet(worksheet, chunk, headerItems, title, startRowIndex);

    self.postMessage({
        type: 'SHEET_CHUNK_PROCESSED',
        sheetIndex: sheetIndex,
        processedRows: chunk.length
    });
}

async function processDataChunk({ chunk, headerItems, title, startRowIndex }) {
    // Use the first (and only) worksheet for single-sheet mode
    const worksheet = worksheets.get(0);
    if (!worksheet) {
        throw new Error('No worksheet available for processing');
    }

    await processDataChunkForWorksheet(worksheet, chunk, headerItems, title, startRowIndex);

    self.postMessage({
        type: 'CHUNK_PROCESSED',
        processedRows: processedRows,
        chunkSize: chunk.length
    });
}

async function processDataChunkForWorksheet(worksheet, chunk, headerItems, title, startRowIndex) {
    const colorMapping = {
        green: '008000',
        red: 'FF0000',
        blue: '0000FF',
        black: '000000',
    };

    for (let i = 0; i < chunk.length; i++) {
        const rowData = chunk[i];
        const row = worksheet.getRow(startRowIndex + i);
        row.height = 25;

        headerItems.forEach((header, colIndex) => {
            const cell = row.getCell(1 + colIndex);
            cell.value = rowData[header];
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
            cell.font = { bold: true };

            // Apply styling based on title and data
            if (rowData.color) {
                applyEmployeeColoring(header, rowData, cell, colorMapping);
            }
            if (title === 'Overall Visits Report') {
                applyOverallVisitsStyle(header, rowData, cell);
            }
            if (title === 'Employee Tracking Report') {
                applyEmployeeTrackingStyle(cell);
            }
            if (title === 'Structure Report') {
                applyStructureDataStyle(header, cell);
            }
        });

        processedRows++;

        // Yield control periodically
        if (i % 100 === 0) {
            await new Promise(resolve => setTimeout(resolve, 0));
        }
    }
}

async function finalizeWorkbook() {
    // Finalize all worksheets
    worksheets.forEach((worksheet) => {
        worksheet.columns.forEach(column => {
            column.width = 20;
        });
    });

    self.postMessage({
        type: 'WORKBOOK_FINALIZED',
        totalRows: processedRows,
        totalSheets: worksheets.size
    });
}

async function generateBuffer() {
    const buffer = await workbook.xlsx.writeBuffer();
    
    self.postMessage({
        type: 'BUFFER_GENERATED',
        buffer: buffer
    }, [buffer]);
}

// Styling helper functions
function applyEmployeeColoring(header, rowData, cell, colorMapping) {
    if (header === 'employee' || header === 'division') {
        const cellColor = colorMapping[rowData.color.toLowerCase()];
        if (cellColor) {
            cell.font = {
                bold: true,
                color: { argb: cellColor },
            };
        }
    }
}

function applyStructureDataStyle(header, cell) {
    if (header === 'brick_name' || header === 'brick_id') {
        cell.font = {
            bold: true,
            color: { argb: '960000' },
        };
    }
}

function applyOverallVisitsStyle(header, rowData, cell) {
    if (header === 'division') {
        cell.font = {
            bold: true,
            color: { argb: '008000' },
        };
    }
    if (header === 'account' || header === 'doctor') {
        cell.font = {
            bold: true,
            color: { argb: '0000ff' },
        };
    }
    if (header === 'acc_type' || header === 'shift') {
        let shiftColor = '000000';
        if (rowData.acc_shift_id === 1) {
            shiftColor = 'EFA609';
        }
        if (rowData.acc_shift_id === 2) {
            shiftColor = 'EF09C2';
        }
        if (rowData.acc_shift_id === 3) {
            shiftColor = '09EFDE';
        }
        cell.font = {
            bold: true,
            color: { argb: shiftColor },
        };
    }
}

function applyEmployeeTrackingStyle(cell) {
    // Add specific styling for employee tracking if needed
    cell.font = { bold: true };
}
